#!/usr/bin/env node

/**
 * Device Compatibility Test Script
 * Verifies that all device compatibility fixes are properly configured
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Testing Android Device Compatibility Fixes...\n');

let allTestsPassed = true;

function testFailed(message) {
  console.log(`❌ ${message}`);
  allTestsPassed = false;
}

function testPassed(message) {
  console.log(`✅ ${message}`);
}

// Test 1: Check if network security config exists
console.log('1. Checking Network Security Configuration...');
const networkConfigPath = path.join(__dirname, '../android/app/src/main/res/xml/network_security_config.xml');
if (fs.existsSync(networkConfigPath)) {
  const networkConfig = fs.readFileSync(networkConfigPath, 'utf8');
  if (networkConfig.includes('cleartextTrafficPermitted="true"')) {
    testPassed('Network security config allows cleartext traffic');
  } else {
    testFailed('Network security config does not allow cleartext traffic');
  }
} else {
  testFailed('Network security config file not found');
}

// Test 2: Check AndroidManifest.xml for required permissions
console.log('\n2. Checking Android Manifest Permissions...');
const manifestPath = path.join(__dirname, '../android/app/src/main/AndroidManifest.xml');
if (fs.existsSync(manifestPath)) {
  const manifest = fs.readFileSync(manifestPath, 'utf8');
  
  const requiredPermissions = [
    'ACCESS_NETWORK_STATE',
    'ACCESS_WIFI_STATE',
    'INTERNET',
    'ACCESS_FINE_LOCATION',
    'ACCESS_COARSE_LOCATION',
    'FOREGROUND_SERVICE'
  ];
  
  let missingPermissions = [];
  requiredPermissions.forEach(permission => {
    if (!manifest.includes(permission)) {
      missingPermissions.push(permission);
    }
  });
  
  if (missingPermissions.length === 0) {
    testPassed('All required permissions are present');
  } else {
    testFailed(`Missing permissions: ${missingPermissions.join(', ')}`);
  }
  
  // Check for network security config reference
  if (manifest.includes('android:networkSecurityConfig="@xml/network_security_config"')) {
    testPassed('Network security config is referenced in manifest');
  } else {
    testFailed('Network security config not referenced in manifest');
  }
  
  // Check for cleartext traffic permission
  if (manifest.includes('android:usesCleartextTraffic="true"')) {
    testPassed('Cleartext traffic is enabled in manifest');
  } else {
    testFailed('Cleartext traffic not enabled in manifest');
  }
} else {
  testFailed('AndroidManifest.xml not found');
}

// Test 3: Check ProGuard rules
console.log('\n3. Checking ProGuard Rules...');
const proguardPath = path.join(__dirname, '../android/app/proguard-rules.pro');
if (fs.existsSync(proguardPath)) {
  const proguardRules = fs.readFileSync(proguardPath, 'utf8');
  
  const requiredRules = [
    'com.facebook.react',
    'expo.modules',
    'com.reactnavigation',
    'com.mapbox'
  ];
  
  let missingRules = [];
  requiredRules.forEach(rule => {
    if (!proguardRules.includes(rule)) {
      missingRules.push(rule);
    }
  });
  
  if (missingRules.length === 0) {
    testPassed('All required ProGuard rules are present');
  } else {
    testFailed(`Missing ProGuard rules for: ${missingRules.join(', ')}`);
  }
} else {
  testFailed('proguard-rules.pro not found');
}

// Test 4: Check build.gradle configuration
console.log('\n4. Checking Build Configuration...');
const buildGradlePath = path.join(__dirname, '../android/app/build.gradle');
if (fs.existsSync(buildGradlePath)) {
  const buildGradle = fs.readFileSync(buildGradlePath, 'utf8');
  
  if (buildGradle.includes('multiDexEnabled true')) {
    testPassed('MultiDex is enabled');
  } else {
    testFailed('MultiDex is not enabled');
  }
  
  if (buildGradle.includes('androidx.multidex:multidex')) {
    testPassed('MultiDex dependency is included');
  } else {
    testFailed('MultiDex dependency is missing');
  }
} else {
  testFailed('build.gradle not found');
}

// Test 5: Check gradle.properties
console.log('\n5. Checking Gradle Properties...');
const gradlePropsPath = path.join(__dirname, '../android/gradle.properties');
if (fs.existsSync(gradlePropsPath)) {
  const gradleProps = fs.readFileSync(gradlePropsPath, 'utf8');
  
  if (gradleProps.includes('android.useAndroidX=true')) {
    testPassed('AndroidX is enabled');
  } else {
    testFailed('AndroidX is not enabled');
  }
  
  if (gradleProps.includes('android.enableJetifier=true')) {
    testPassed('Jetifier is enabled');
  } else {
    testFailed('Jetifier is not enabled');
  }

  // Check for deprecated options
  if (gradleProps.includes('android.enableDexingArtifactTransform.desugaring')) {
    testFailed('Deprecated dexing option found - this will cause build failures');
  } else {
    testPassed('No deprecated dexing options found');
  }
} else {
  testFailed('gradle.properties not found');
}

// Test 6: Check if DeviceLogger exists
console.log('\n6. Checking Device Logger...');
const deviceLoggerPath = path.join(__dirname, '../src/utils/DeviceLogger.js');
if (fs.existsSync(deviceLoggerPath)) {
  testPassed('DeviceLogger utility is present');
} else {
  testFailed('DeviceLogger utility is missing');
}

// Test 7: Check if expo-device is installed
console.log('\n7. Checking Dependencies...');
const packageJsonPath = path.join(__dirname, '../package.json');
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  if (packageJson.dependencies['expo-device']) {
    testPassed('expo-device dependency is installed');
  } else {
    testFailed('expo-device dependency is missing');
  }
  
  const requiredDeps = [
    '@react-native-community/netinfo',
    '@react-native-async-storage/async-storage',
    'expo-device'
  ];
  
  let missingDeps = [];
  requiredDeps.forEach(dep => {
    if (!packageJson.dependencies[dep]) {
      missingDeps.push(dep);
    }
  });
  
  if (missingDeps.length === 0) {
    testPassed('All required dependencies are installed');
  } else {
    testFailed(`Missing dependencies: ${missingDeps.join(', ')}`);
  }
} else {
  testFailed('package.json not found');
}

// Summary
console.log('\n' + '='.repeat(50));
if (allTestsPassed) {
  console.log('🎉 All device compatibility tests passed!');
  console.log('Your app should now work properly on real Android devices.');
  console.log('\nNext steps:');
  console.log('1. Clean build: expo run:android --clear');
  console.log('2. Test on real device: expo run:android --device');
  console.log('3. Create release build: eas build --profile production --platform android');
} else {
  console.log('⚠️  Some tests failed. Please review the issues above.');
  console.log('Refer to DEVICE_FIXES_README.md for detailed instructions.');
  process.exit(1);
}
console.log('='.repeat(50));
