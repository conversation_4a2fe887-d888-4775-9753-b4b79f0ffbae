{"name": "paiwat", "version": "1.0.5", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/progress-view": "^1.5.0", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "@rnmapbox/maps": "^10.1.40", "@supabase/supabase-js": "^2.53.0", "expo": "~53.0.9", "expo-av": "~15.1.7", "expo-device": "^7.1.4", "expo-font": "^13.3.1", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-location": "~18.1.6", "expo-sms": "^13.1.4", "expo-splash-screen": "^0.30.10", "expo-status-bar": "~2.2.3", "expo-task-manager": "~13.1.6", "expo-updates": "~0.28.17", "node-fetch": "^3.3.2", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "dotenv": "^17.2.1"}, "private": true}