import 'react-native-gesture-handler';
import React, { useState, useEffect, useCallback } from 'react';
import { StatusBar } from 'expo-status-bar';
import { View, Text, StyleSheet, AppState } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import * as Font from 'expo-font';
import * as SplashScreen from 'expo-splash-screen';
import AppNavigator from './src/navigation/AppNavigator';
import { COLORS } from './src/utils/constants';
import deviceLogger, { logLifecycle, logError } from './src/utils/DeviceLogger';
import ErrorBoundary from './src/components/ErrorBoundary';
import { ToastProvider } from './src/contexts/ToastContext';

// Keep the splash screen visible while we fetch resources
  try { SplashScreen.preventAutoHideAsync(); } catch (e) { console.warn('preventAutoHideAsync failed', e); }


export default function App() {
  const [appIsReady, setAppIsReady] = useState(false);

  useEffect(() => {
    async function prepare() {
      try {
        // Log app initialization
        await logLifecycle('App initialization started');

        // Pre-load fonts, make any API calls you need to do here
        await Font.loadAsync({
          'Poppins-Light': require('./assets/fonts/Poppins-Light.ttf'),
          'Poppins-Regular': require('./assets/fonts/Poppins-Regular.ttf'),
          'Poppins-Medium': require('./assets/fonts/Poppins-Medium.ttf'),
          'Poppins-Bold': require('./assets/fonts/Poppins-Bold.ttf'),
        });

        await logLifecycle('Fonts loaded successfully');

        // Removed artificial delay to minimize time between OS splash and app content on Android
        // await new Promise(resolve => setTimeout(resolve, 500));
      } catch (e) {
        console.warn('Error loading resources:', e);
        await logError(e, 'App initialization error');
      } finally {
        // Tell the application to render
        setAppIsReady(true);
        await logLifecycle('App ready to render');
      }
    }

    prepare();
  }, []);

  // Handle app state changes
  useEffect(() => {
    const handleAppStateChange = async (nextAppState) => {
      await logLifecycle(`App state changed to: ${nextAppState}`);
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, []);

  const onLayoutRootView = useCallback(async () => {
    if (appIsReady) {
      // Hide splash screen immediately when ready
      await SplashScreen.hideAsync();
    }
  }, [appIsReady]);

  if (!appIsReady) {
    return null;
  }



  return (
    <SafeAreaProvider>
      <ErrorBoundary
        title="App Error"
        message="Something went wrong with the app. Please restart and try again."
      >
        <ToastProvider>
          <View style={{ flex: 1, backgroundColor: COLORS.background }} onLayout={onLayoutRootView}>
            <AppNavigator />
            <StatusBar style="dark" />
          </View>
        </ToastProvider>
      </ErrorBoundary>
    </SafeAreaProvider>
  );
}


