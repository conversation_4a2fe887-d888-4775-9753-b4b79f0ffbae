{"expo": {"name": "paiwat", "slug": "paiwat", "version": "1.0.5", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": false, "splash": {"image": "./assets/splashscreen.png", "resizeMode": "cover", "backgroundColor": "#ffffff"}, "androidStatusBar": {"backgroundColor": "#ffffff", "barStyle": "dark-content", "translucent": false}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.paiwat.app", "buildNumber": "6"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "splash": {"image": "./assets/splashscreen.png", "resizeMode": "cover", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.paiwat.app", "versionCode": 3, "permissions": ["ACCESS_COARSE_LOCATION", "ACCESS_FINE_LOCATION", "INTERNET"]}, "web": {"favicon": "./assets/favicon.png"}}}