/**
 * LocalDataService - Loads data from multiple sources organized by category
 *
 * Data Sources:
 * - Forts: Remote (Backblaze B2 trek-json bucket) - cloud-only (20 items)
 * - Waterfalls: Remote (Backblaze B2 waterfall-json bucket) - cloud-only (8 items)
 * - Treks: Remote (Backblaze B2 treksjson bucket) - cloud-only (4 items)
 * - Caves: Remote (Backblaze B2 caves-json bucket) - cloud-only (5 items)
 */

import RemoteDataService from './RemoteDataService';

// All data now loaded from Backblaze B2 cloud storage
// - Forts: trek-json bucket
// - Waterfalls: waterfall-json bucket  
// - Treks: treksjson bucket
// - Caves: caves-json bucket

// Note: tikona.json and torna.json have been consolidated into the main data files

class LocalDataService {
  static allData = null;
  static dataByCategory = null;
  static initialized = false;

  /**
   * Initialize and combine all data from multiple sources
   */
  static async initializeData() {
    if (this.initialized) {
      return this.allData;
    }

    try {
      // Get fort data from remote service (cloud-only)

      const forts = await RemoteDataService.getFortData();

      // Get waterfall data from remote service (cloud-only) - optional
      let waterfalls = [];
      try {
        waterfalls = await RemoteDataService.getWaterfallData();
      } catch (waterfallError) {
        // Continue without waterfall data - it's optional
        // Error will be handled by the UI layer
      }

      // Get trek data from remote service (cloud-only)
      let treks = [];
      try {
        treks = await RemoteDataService.getTrekData();
        console.log(`✅ Loaded ${treks.length} treks from Backblaze B2`);
      } catch (trekError) {
        console.warn('Failed to load trek data:', trekError.message);
        // Continue without trek data - cloud-only mode
      }

      // Get cave data from remote service (cloud-only)
      let caves = [];
      try {
        caves = await RemoteDataService.getCaveData();
        console.log(`✅ Loaded ${caves.length} caves from Backblaze B2`);
      } catch (caveError) {
        console.warn('Failed to load cave data:', caveError.message);
        // Continue without cave data - cloud-only mode
      }

      // Ensure each item has the correct category and coordinates - preserve original categories but normalize for map display
      forts.forEach(fort => {
        if (!fort.category) fort.category = 'fort';
        // Store original category for reference
        if (!fort.originalCategory) fort.originalCategory = fort.category;
        // Normalize fort categories for map display
        if (fort.category === 'fort' || fort.category.includes('fort')) {
          fort.mapCategory = 'fort';
        } else {
          fort.mapCategory = 'fort'; // Default for forts array
        }
        // Extract coordinates from startingPoint for map compatibility
        if (fort.startingPoint && fort.startingPoint.coordinates && !fort.coordinates) {
          fort.coordinates = fort.startingPoint.coordinates;
        }
      });

      treks.forEach(trek => {
        // Store original category for reference
        if (!trek.originalCategory) trek.originalCategory = trek.category;
        // Normalize trek-related categories for map display
        if (!trek.category ||
            trek.category === 'peak' ||
            trek.category === 'jungle trek' ||
            trek.category.includes('trek')) {
          trek.category = 'trek';
          trek.mapCategory = 'trek';
        } else if (trek.category === 'fort' || trek.category.includes('fort')) {
          // Keep fort category for items like Rajmachi
          trek.mapCategory = 'fort';
        } else {
          trek.category = 'trek'; // Default for treks array
          trek.mapCategory = 'trek';
        }
        // Extract coordinates from startingPoint for map compatibility
        if (trek.startingPoint && trek.startingPoint.coordinates && !trek.coordinates) {
          trek.coordinates = trek.startingPoint.coordinates;
        }
      });

      waterfalls.forEach(waterfall => {
        if (!waterfall.category) waterfall.category = 'waterfall';
        // Store original category for reference
        if (!waterfall.originalCategory) waterfall.originalCategory = waterfall.category;
        waterfall.mapCategory = 'waterfall';
        // Extract coordinates from startingPoint for map compatibility
        if (waterfall.startingPoint && waterfall.startingPoint.coordinates && !waterfall.coordinates) {
          waterfall.coordinates = waterfall.startingPoint.coordinates;
        }
      });

      caves.forEach(cave => {
        if (!cave.category) cave.category = 'cave';
        // Store original category for reference
        if (!cave.originalCategory) cave.originalCategory = cave.category;
        cave.mapCategory = 'cave';
        // Extract coordinates from startingPoint for map compatibility
        if (cave.startingPoint && cave.startingPoint.coordinates && !cave.coordinates) {
          cave.coordinates = cave.startingPoint.coordinates;
        }
      });

      // Combine all data
      this.allData = [
        ...forts,
        ...treks,
        ...waterfalls,
        ...caves,
      ];

      console.log('=== DATA LOADING SUMMARY ===');
      console.log(`Forts: ${forts.length}`);
      console.log(`Treks: ${treks.length}`);
      console.log(`Waterfalls: ${waterfalls.length}`);
      console.log(`Caves: ${caves.length}`);
      console.log(`Total: ${this.allData.length}`);
      console.log('=== END SUMMARY ===');

      // Validate data integrity
      const allIds = this.allData.map(item => item.id);
      const uniqueIds = [...new Set(allIds)];
      if (allIds.length !== uniqueIds.length) {
        // Handle duplicate IDs by keeping only the first occurrence
        const seen = new Set();
        this.allData = this.allData.filter(item => {
          if (seen.has(item.id)) {
            return false;
          }
          seen.add(item.id);
          return true;
        });
      }

      // Create category-wise data
      this.dataByCategory = {
        fort: forts,
        trek: treks,
        waterfall: waterfalls,
        cave: caves,
      };

      this.initialized = true;

      return this.allData;
    } catch (error) {
      // In cloud-only mode, we don't have local fallbacks
      // Set empty arrays and throw error to indicate failure
      this.allData = [];
      this.dataByCategory = { fort: [], trek: [], waterfall: [], cave: [] };

      // Create a user-friendly error message
      const errorMessage = error.message?.includes('network') || error.message?.includes('fetch')
        ? 'Unable to connect to the server. Please check your internet connection.'
        : 'Unable to load data. Please try again later.';

      throw new Error(errorMessage);
    }
  }

  /**
   * Get all data combined
   */
  static async getAllData() {
    if (!this.initialized) {
      await this.initializeData();
    }
    return this.allData || [];
  }

  /**
   * Get data by category
   */
  static async getDataByCategory(category) {
    if (!this.initialized) {
      await this.initializeData();
    }
    return this.dataByCategory[category] || [];
  }

  /**
   * Get featured items across all categories
   */
  static async getFeaturedData(limit = 10) {
    const allData = await this.getAllData();
    return allData
      .filter(item => item.featured === true)
      .sort((a, b) => (b.rating || 0) - (a.rating || 0))
      .slice(0, limit);
  }

  /**
   * Get popular items (high rating) across all categories
   */
  static async getPopularData(limit = 10) {
    const allData = await this.getAllData();
    return allData
      .filter(item => item.rating && item.rating >= 4.0)
      .sort((a, b) => (b.rating || 0) - (a.rating || 0))
      .slice(0, limit);
  }

  /**
   * Get items by difficulty level
   */
  static async getDataByDifficulty(difficulty) {
    const allData = await this.getAllData();
    return allData.filter(item =>
      item.difficulty &&
      item.difficulty.toLowerCase().includes(difficulty.toLowerCase())
    );
  }

  /**
   * Search data by name or location
   */
  static async searchData(query) {
    if (!query || query.trim().length < 2) {
      return [];
    }

    const allData = await this.getAllData();
    const searchTerm = query.toLowerCase().trim();

    return allData.filter(item =>
      (item.name && item.name.toLowerCase().includes(searchTerm)) ||
      (item.location && item.location.toLowerCase().includes(searchTerm)) ||
      (item.description && item.description.toLowerCase().includes(searchTerm))
    );
  }

  /**
   * Get item by ID
   */
  static async getItemById(id) {
    const allData = await this.getAllData();
    return allData.find(item => item.id === id || item.id === parseInt(id));
  }

  /**
   * Get items with coordinates (for map and nearby features)
   */
  static async getItemsWithCoordinates() {
    const allData = await this.getAllData();
    return allData.filter(item =>
      item.coordinates &&
      item.coordinates.latitude &&
      item.coordinates.longitude
    );
  }

  /**
   * Get data statistics
   */
  static async getDataStats() {
    const allData = await this.getAllData();
    const byCategory = this.dataByCategory || {};

    return {
      total: allData.length,
      byCategory: {
        forts: (byCategory.fort || []).length,
        treks: (byCategory.trek || []).length,
        waterfalls: (byCategory.waterfall || []).length,
        caves: (byCategory.cave || []).length,
      },
      featured: allData.filter(item => item.featured).length,
      withCoordinates: allData.filter(item =>
        item.coordinates && item.coordinates.latitude
      ).length,
      avgRating: allData.length > 0 ?
        allData.reduce((sum, item) => sum + (item.rating || 0), 0) / allData.length : 0,
    };
  }

  /**
   * Get random items for discovery
   */
  static async getRandomData(limit = 5, category = null) {
    let data = category ? await this.getDataByCategory(category) : await this.getAllData();

    // Shuffle array and return limited results
    const shuffled = [...data].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, limit);
  }

  /**
   * Get items by location/district
   */
  static async getDataByLocation(location) {
    const allData = await this.getAllData();
    return allData.filter(item =>
      item.location &&
      item.location.toLowerCase().includes(location.toLowerCase())
    );
  }

  /**
   * Get top rated items by category
   */
  static async getTopRatedByCategory(category, limit = 5) {
    const categoryData = await this.getDataByCategory(category);
    return categoryData
      .filter(item => item.rating)
      .sort((a, b) => (b.rating || 0) - (a.rating || 0))
      .slice(0, limit);
  }

  /**
   * Get items suitable for beginners
   */
  static async getBeginnerFriendlyData(limit = 10) {
    const allData = await this.getAllData();
    return allData
      .filter(item =>
        item.difficulty &&
        (item.difficulty.toLowerCase().includes('easy') ||
         item.difficulty.toLowerCase().includes('beginner'))
      )
      .sort((a, b) => (b.rating || 0) - (a.rating || 0))
      .slice(0, limit);
  }

  /**
   * Force refresh all data from cloud storage
   */
  static async forceRefreshData() {
    try {

      // Clear the initialized flag to force re-initialization
      this.initialized = false;
      this.allData = null;
      this.dataByCategory = null;

      // Clear RemoteDataService cache
      await RemoteDataService.clearCache();

      // Re-initialize with fresh data
      await this.initializeData();

      return true;
    } catch (error) {
      // Create user-friendly error message
      const errorMessage = error.message?.includes('network') || error.message?.includes('fetch')
        ? 'Unable to refresh data. Please check your internet connection.'
        : 'Failed to refresh data. Please try again.';

      throw new Error(errorMessage);
    }
  }
}

export default LocalDataService;
