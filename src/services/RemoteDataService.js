import AsyncStorage from '@react-native-async-storage/async-storage';
import BackblazeB2Service from './BackblazeB2Service';

/**
 * RemoteDataService - Service for fetching and caching remote fort data
 * 
 * Features:
 * - Fetch fort data from Backblaze B2
 * - Local caching with expiration
 * - Fallback to local data when remote fails
 * - Batch processing for multiple files
 * - Data validation and error handling
 */
class RemoteDataService {
  constructor() {
    this.cachePrefix = 'remote_data_';
    this.cacheExpiryPrefix = 'remote_expiry_';
    this.defaultCacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

    // Cache keys
    this.FORT_DATA_CACHE_KEY = 'remote_fort_data';
    this.FORT_LIST_CACHE_KEY = 'remote_fort_list';
    this.WATERFALL_DATA_CACHE_KEY = 'remote_waterfall_data';
    this.WATERFALL_LIST_CACHE_KEY = 'remote_waterfall_list';
    this.TREK_DATA_CACHE_KEY = 'remote_trek_data';
    this.TREK_LIST_CACHE_KEY = 'remote_trek_list';
    this.CAVE_DATA_CACHE_KEY = 'remote_cave_data';
    this.CAVE_LIST_CACHE_KEY = 'remote_cave_list';
    this.LAST_SYNC_KEY = 'remote_last_sync';

    this.isInitialized = false;
  }

  /**
   * Initialize the service - cloud-only mode
   */
  async initialize() {
    if (this.isInitialized) return;

    this.isInitialized = true;
  }

  /**
   * Check if cached data is valid
   */
  async isCacheValid(cacheKey, customExpiry = null) {
    try {
      const expiryKey = `${this.cacheExpiryPrefix}${cacheKey}`;
      const expiryTime = await AsyncStorage.getItem(expiryKey);
      
      if (!expiryTime) return false;
      
      const expiry = new Date(expiryTime);
      const now = new Date();
      
      return now.getTime() < expiry.getTime();
    } catch (error) {
      return false;
    }
  }

  /**
   * Get cached data
   */
  async getCachedData(cacheKey) {
    try {
      const cachedData = await AsyncStorage.getItem(`${this.cachePrefix}${cacheKey}`);
      return cachedData ? JSON.parse(cachedData) : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Set cached data with expiry
   */
  async setCachedData(cacheKey, data, customExpiry = null) {
    try {
      const expiry = customExpiry || this.defaultCacheExpiry;
      const expiryTime = new Date(Date.now() + expiry);
      
      await Promise.all([
        AsyncStorage.setItem(`${this.cachePrefix}${cacheKey}`, JSON.stringify(data)),
        AsyncStorage.setItem(`${this.cacheExpiryPrefix}${cacheKey}`, expiryTime.toISOString())
      ]);
      
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get list of fort files from Backblaze B2
   */
  async getFortFileList(useCache = true) {
    try {
      // Check cache first
      if (useCache && await this.isCacheValid(this.FORT_LIST_CACHE_KEY)) {
        const cachedList = await this.getCachedData(this.FORT_LIST_CACHE_KEY);
        if (cachedList && cachedList.length > 0) {
          return cachedList;
        }
      }

      // List all files in the bucket
      const allFiles = await BackblazeB2Service.listFiles('', 'trek-json');

      // The files are directly in the bucket root, not in a forts/ subfolder
      // Filter all JSON files as potential fort files
      let fortFiles = allFiles.filter(file => file.fileName.endsWith('.json'));

      // Map to our expected format
      fortFiles = fortFiles.map(file => ({
        fileName: file.fileName,
        fileId: file.fileId,
        size: file.size,
        uploadTimestamp: file.uploadTimestamp
      }));


      // Cache the file list
      await this.setCachedData(this.FORT_LIST_CACHE_KEY, fortFiles, 6 * 60 * 60 * 1000); // 6 hours cache

      return fortFiles;

    } catch (error) {
      // Return cached data if available, even if expired
      const cachedList = await this.getCachedData(this.FORT_LIST_CACHE_KEY);
      if (cachedList && cachedList.length > 0) {
        return cachedList;
      }

      throw new Error('Unable to connect to server. Please check your internet connection.');
    }
  }

  /**
   * Fetch all fort data from Backblaze B2 - cloud-only mode
   */
  async fetchFortData(forceRefresh = false) {
    try {
      await this.initialize();

      // Check cache first
      if (!forceRefresh && await this.isCacheValid(this.FORT_DATA_CACHE_KEY)) {
        const cachedData = await this.getCachedData(this.FORT_DATA_CACHE_KEY);
        if (cachedData && cachedData.length > 0) {
          return cachedData;
        }
      }


      // Get list of fort files
      const fortFiles = await this.getFortFileList();

      if (!fortFiles || fortFiles.length === 0) {
        throw new Error('No fort files found in Backblaze B2 trek-json bucket');
      }

      // Download all fort files from trek-json bucket
      const fileNames = fortFiles.map(file => file.fileName);
      const { results, errors } = await BackblazeB2Service.downloadFiles(fileNames, 'trek-json');

      // Process successful downloads
      const fortData = results
        .filter(result => result.success && result.data)
        .map(result => result.data)
        .filter(fort => fort && fort.id); // Validate fort data

      if (fortData.length === 0) {
        throw new Error('No valid fort data downloaded from Backblaze B2');
      }

      // Continue even if some files failed to download

      // Cache the fort data
      await this.setCachedData(this.FORT_DATA_CACHE_KEY, fortData);

      // Update last sync time
      await AsyncStorage.setItem(this.LAST_SYNC_KEY, new Date().toISOString());

      return fortData;

    } catch (error) {
      // Try to return cached data even if expired
      const cachedData = await this.getCachedData(this.FORT_DATA_CACHE_KEY);
      if (cachedData && cachedData.length > 0) {
        return cachedData;
      }

      throw new Error('Unable to load fort data. Please check your internet connection.');
    }
  }

  /**
   * Get fort data - cloud-only mode
   */
  async getFortData(forceRefresh = false) {
    return await this.fetchFortData(forceRefresh);
  }

  /**
   * Get list of waterfall files from Backblaze B2 waterfalls bucket
   */
  async getWaterfallFileList(useCache = true) {
    try {
      // Check cache first
      if (useCache && await this.isCacheValid(this.WATERFALL_LIST_CACHE_KEY)) {
        const cachedList = await this.getCachedData(this.WATERFALL_LIST_CACHE_KEY);
        if (cachedList && cachedList.length > 0) {
          return cachedList;
        }
      }

      // List all files in the bucket
      const allFiles = await BackblazeB2Service.listFiles('', 'waterfall-json');

      // The files are directly in the bucket root, not in a waterfalls/ subfolder
      // Filter all JSON files as potential waterfall files
      let waterfallFiles = allFiles.filter(file => file.fileName.endsWith('.json'));

      // Map to the expected format
      waterfallFiles = waterfallFiles.map(file => ({
        fileName: file.fileName,
        fileId: file.fileId,
        size: file.size,
        uploadTimestamp: file.uploadTimestamp
      }));

      // Cache the file list
      await this.setCachedData(this.WATERFALL_LIST_CACHE_KEY, waterfallFiles, 6 * 60 * 60 * 1000); // 6 hours cache

      return waterfallFiles;

    } catch (error) {


      // Return cached data if available, even if expired
      const cachedList = await this.getCachedData(this.WATERFALL_LIST_CACHE_KEY);
      if (cachedList && cachedList.length > 0) {
        return cachedList;
      }

      throw error;
    }
  }

  /**
   * Fetch all waterfall data from Backblaze B2 - cloud-only mode
   */
  async fetchWaterfallData(forceRefresh = false) {
    try {
      await this.initialize();

      // Check cache first
      if (!forceRefresh && await this.isCacheValid(this.WATERFALL_DATA_CACHE_KEY)) {
        const cachedData = await this.getCachedData(this.WATERFALL_DATA_CACHE_KEY);
        if (cachedData && cachedData.length > 0) {
          return cachedData;
        }
      }


      // Get list of waterfall files
      const waterfallFiles = await this.getWaterfallFileList();

      if (!waterfallFiles || waterfallFiles.length === 0) {
        throw new Error('No waterfall files found in Backblaze B2 waterfall-json bucket');
      }

      // Download all waterfall files from waterfall-json bucket
      const fileNames = waterfallFiles.map(file => file.fileName);
      const { results, errors } = await BackblazeB2Service.downloadFiles(fileNames, 'waterfall-json');

      // Process successful downloads
      const waterfallData = results
        .filter(result => result.success && result.data)
        .map(result => result.data)
        .filter(waterfall => waterfall && waterfall.id); // Validate waterfall data

      if (waterfallData.length === 0) {
        throw new Error('No valid waterfall data downloaded from Backblaze B2');
      }

      // Log any errors
      if (errors.length > 0) {

      }

      // Cache the waterfall data
      await this.setCachedData(this.WATERFALL_DATA_CACHE_KEY, waterfallData);

      // Update last sync time
      await AsyncStorage.setItem(this.LAST_SYNC_KEY, new Date().toISOString());

      return waterfallData;

    } catch (error) {


      // Try to return cached data even if expired
      const cachedData = await this.getCachedData(this.WATERFALL_DATA_CACHE_KEY);
      if (cachedData && cachedData.length > 0) {
        return cachedData;
      }

      // No fallback - throw error to indicate cloud data is unavailable
      throw new Error(`Failed to fetch waterfall data from Backblaze B2: ${error.message}`);
    }
  }

  /**
   * Get waterfall data - cloud-only mode
   */
  async getWaterfallData(forceRefresh = false) {
    return await this.fetchWaterfallData(forceRefresh);
  }

  /**
   * Get list of trek files from Backblaze B2 treksjson bucket
   */
  async getTrekFileList(useCache = true) {
    try {
      // Check cache first
      if (useCache && await this.isCacheValid(this.TREK_LIST_CACHE_KEY)) {
        const cachedList = await this.getCachedData(this.TREK_LIST_CACHE_KEY);
        if (cachedList && cachedList.length > 0) {
          return cachedList;
        }
      }

      // List all files in the bucket
      const allFiles = await BackblazeB2Service.listFiles('', 'treksjson');

      // The files are directly in the bucket root, not in a treks/ subfolder
      // Filter all JSON files as potential trek files
      let trekFiles = allFiles.filter(file => file.fileName.endsWith('.json'));

      // Map to the expected format
      trekFiles = trekFiles.map(file => ({
        fileName: file.fileName,
        fileId: file.fileId,
        size: file.size,
        uploadTimestamp: file.uploadTimestamp
      }));

      // Cache the file list
      await this.setCachedData(this.TREK_LIST_CACHE_KEY, trekFiles, 6 * 60 * 60 * 1000); // 6 hours cache

      return trekFiles;

    } catch (error) {


      // Return cached data if available, even if expired
      const cachedList = await this.getCachedData(this.TREK_LIST_CACHE_KEY);
      if (cachedList && cachedList.length > 0) {
        return cachedList;
      }

      throw error;
    }
  }

  /**
   * Fetch all trek data from Backblaze B2 - cloud-only mode
   */
  async fetchTrekData(forceRefresh = false) {
    try {
      await this.initialize();

      // Check cache first
      if (!forceRefresh && await this.isCacheValid(this.TREK_DATA_CACHE_KEY)) {
        const cachedData = await this.getCachedData(this.TREK_DATA_CACHE_KEY);
        if (cachedData && cachedData.length > 0) {
          return cachedData;
        }
      }


      // Get list of trek files
      const trekFiles = await this.getTrekFileList();

      if (!trekFiles || trekFiles.length === 0) {
        throw new Error('No trek files found in Backblaze B2 treksjson bucket');
      }

      // Download all trek files from treksjson bucket
      const fileNames = trekFiles.map(file => file.fileName);
      const { results, errors } = await BackblazeB2Service.downloadFiles(fileNames, 'treksjson');

      // Process successful downloads
      const trekData = results
        .filter(result => result.success && result.data)
        .map(result => result.data)
        .filter(trek => trek && trek.id); // Validate trek data

      if (trekData.length === 0) {
        throw new Error('No valid trek data downloaded from Backblaze B2');
      }

      // Log any errors
      if (errors.length > 0) {

      }

      // Cache the trek data
      await this.setCachedData(this.TREK_DATA_CACHE_KEY, trekData);

      // Update last sync time
      await AsyncStorage.setItem(this.LAST_SYNC_KEY, new Date().toISOString());

      return trekData;

    } catch (error) {


      // Try to return cached data even if expired
      const cachedData = await this.getCachedData(this.TREK_DATA_CACHE_KEY);
      if (cachedData && cachedData.length > 0) {
        return cachedData;
      }

      // No fallback - throw error to indicate cloud data is unavailable
      throw new Error(`Failed to fetch trek data from Backblaze B2: ${error.message}`);
    }
  }

  /**
   * Get trek data - cloud-only mode
   */
  async getTrekData(forceRefresh = false) {
    return await this.fetchTrekData(forceRefresh);
  }

  /**
   * Get list of cave files from Backblaze B2 caves bucket
   */
  async getCaveFileList(useCache = true) {
    try {
      // Check cache first
      if (useCache && await this.isCacheValid(this.CAVE_LIST_CACHE_KEY)) {
        const cachedList = await this.getCachedData(this.CAVE_LIST_CACHE_KEY);
        if (cachedList && cachedList.length > 0) {
          return cachedList;
        }
      }

      // List all files in the bucket
      const allFiles = await BackblazeB2Service.listFiles('', 'caves-json');

      // The files are directly in the bucket root, not in a caves/ subfolder
      // Filter all JSON files as potential cave files
      let caveFiles = allFiles.filter(file => file.fileName.endsWith('.json'));

      // Map to the expected format
      caveFiles = caveFiles.map(file => ({
        fileName: file.fileName,
        fileId: file.fileId,
        size: file.size,
        uploadTimestamp: file.uploadTimestamp
      }));

      // Cache the file list
      await this.setCachedData(this.CAVE_LIST_CACHE_KEY, caveFiles, 6 * 60 * 60 * 1000); // 6 hours cache

      return caveFiles;

    } catch (error) {
      console.error('Error getting cave file list:', error);

      // If bucket not found, try refreshing authentication once
      if (error.message.includes('not found or not configured')) {
        console.log('🔄 Bucket not found, trying to refresh authentication...');
        try {
          await BackblazeB2Service.forceRefreshAuth();
          console.log('✓ Authentication refreshed, retrying cave file list...');
          
          // Retry once after auth refresh
          const allFiles = await BackblazeB2Service.listFiles('', 'caves-json');
          let caveFiles = allFiles.filter(file => file.fileName.endsWith('.json'));
          
          caveFiles = caveFiles.map(file => ({
            fileName: file.fileName,
            fileId: file.fileId,
            size: file.size,
            uploadTimestamp: file.uploadTimestamp
          }));

          await this.setCachedData(this.CAVE_LIST_CACHE_KEY, caveFiles, 6 * 60 * 60 * 1000);
          console.log(`✅ Successfully found ${caveFiles.length} cave files after auth refresh`);
          return caveFiles;
          
        } catch (retryError) {
          console.error('❌ Still failed after auth refresh:', retryError);
        }
      }

      // Return cached data if available, even if expired
      const cachedList = await this.getCachedData(this.CAVE_LIST_CACHE_KEY);
      if (cachedList && cachedList.length > 0) {
        console.log(`📦 Returning ${cachedList.length} cached cave files due to error`);
        return cachedList;
      }

      throw error;
    }
  }

  /**
   * Fetch all cave data from Backblaze B2 - cloud-only mode
   */
  async fetchCaveData(forceRefresh = false) {
    try {
      await this.initialize();

      // Check cache first
      if (!forceRefresh && await this.isCacheValid(this.CAVE_DATA_CACHE_KEY)) {
        const cachedData = await this.getCachedData(this.CAVE_DATA_CACHE_KEY);
        if (cachedData && cachedData.length > 0) {
          console.log(`Returning ${cachedData.length} cached caves`);
          return cachedData;
        }
      }

      console.log('Fetching cave data from Backblaze B2 caves-json bucket...');

      // Get list of cave files
      const caveFiles = await this.getCaveFileList();

      if (!caveFiles || caveFiles.length === 0) {
        throw new Error('No cave files found in Backblaze B2 caves-json bucket');
      }

      console.log(`Found ${caveFiles.length} cave files in caves-json bucket`);

      // Download all cave files from caves-json bucket
      const fileNames = caveFiles.map(file => file.fileName);
      const { results, errors } = await BackblazeB2Service.downloadFiles(fileNames, 'caves-json');

      // Process successful downloads
      const caveData = results
        .filter(result => result.success && result.data)
        .map(result => result.data)
        .filter(cave => cave && cave.id); // Validate cave data

      if (caveData.length === 0) {
        throw new Error('No valid cave data downloaded from Backblaze B2');
      }

      console.log(`Successfully downloaded ${caveData.length} caves from Backblaze B2`);

      // Log any errors
      if (errors.length > 0) {
        console.warn(`Failed to download ${errors.length} cave files:`, errors);
      }

      // Cache the cave data
      await this.setCachedData(this.CAVE_DATA_CACHE_KEY, caveData);

      // Update last sync time
      await AsyncStorage.setItem(this.LAST_SYNC_KEY, new Date().toISOString());

      return caveData;

    } catch (error) {
      console.error('Error fetching cave data:', error);

      // Try to return cached data even if expired
      const cachedData = await this.getCachedData(this.CAVE_DATA_CACHE_KEY);
      if (cachedData && cachedData.length > 0) {
        console.log(`Returning ${cachedData.length} expired cached caves due to fetch error`);
        return cachedData;
      }

      // No fallback - throw error to indicate cloud data is unavailable
      throw new Error(`Failed to fetch cave data from Backblaze B2: ${error.message}`);
    }
  }

  /**
   * Get cave data - cloud-only mode
   */
  async getCaveData(forceRefresh = false) {
    return await this.fetchCaveData(forceRefresh);
  }

  /**
   * Clear all cached data
   */
  async clearCache() {
    try {
      const keys = [
        `${this.cachePrefix}${this.FORT_DATA_CACHE_KEY}`,
        `${this.cachePrefix}${this.FORT_LIST_CACHE_KEY}`,
        `${this.cachePrefix}${this.WATERFALL_DATA_CACHE_KEY}`,
        `${this.cachePrefix}${this.WATERFALL_LIST_CACHE_KEY}`,
        `${this.cachePrefix}${this.TREK_DATA_CACHE_KEY}`,
        `${this.cachePrefix}${this.TREK_LIST_CACHE_KEY}`,
        `${this.cachePrefix}${this.CAVE_DATA_CACHE_KEY}`,
        `${this.cachePrefix}${this.CAVE_LIST_CACHE_KEY}`,
        `${this.cacheExpiryPrefix}${this.FORT_DATA_CACHE_KEY}`,
        `${this.cacheExpiryPrefix}${this.FORT_LIST_CACHE_KEY}`,
        `${this.cacheExpiryPrefix}${this.WATERFALL_DATA_CACHE_KEY}`,
        `${this.cacheExpiryPrefix}${this.WATERFALL_LIST_CACHE_KEY}`,
        `${this.cacheExpiryPrefix}${this.TREK_DATA_CACHE_KEY}`,
        `${this.cacheExpiryPrefix}${this.TREK_LIST_CACHE_KEY}`,
        `${this.cacheExpiryPrefix}${this.CAVE_DATA_CACHE_KEY}`,
        `${this.cacheExpiryPrefix}${this.CAVE_LIST_CACHE_KEY}`,
        this.LAST_SYNC_KEY
      ];

      await AsyncStorage.multiRemove(keys);

    } catch (error) {

    }
  }

  /**
   * Get cache status and statistics
   */
  async getCacheStatus() {
    try {
      const [fortDataValid, fortListValid, waterfallDataValid, waterfallListValid, trekDataValid, trekListValid, lastSync] = await Promise.all([
        this.isCacheValid(this.FORT_DATA_CACHE_KEY),
        this.isCacheValid(this.FORT_LIST_CACHE_KEY),
        this.isCacheValid(this.WATERFALL_DATA_CACHE_KEY),
        this.isCacheValid(this.WATERFALL_LIST_CACHE_KEY),
        this.isCacheValid(this.TREK_DATA_CACHE_KEY),
        this.isCacheValid(this.TREK_LIST_CACHE_KEY),
        AsyncStorage.getItem(this.LAST_SYNC_KEY)
      ]);

      const [cachedFortData, cachedWaterfallData, cachedTrekData] = await Promise.all([
        this.getCachedData(this.FORT_DATA_CACHE_KEY),
        this.getCachedData(this.WATERFALL_DATA_CACHE_KEY),
        this.getCachedData(this.TREK_DATA_CACHE_KEY)
      ]);

      return {
        fortDataCached: !!cachedFortData,
        fortDataValid: fortDataValid,
        fortListValid: fortListValid,
        cachedFortCount: cachedFortData ? cachedFortData.length : 0,
        waterfallDataCached: !!cachedWaterfallData,
        waterfallDataValid: waterfallDataValid,
        waterfallListValid: waterfallListValid,
        cachedWaterfallCount: cachedWaterfallData ? cachedWaterfallData.length : 0,
        trekDataCached: !!cachedTrekData,
        trekDataValid: trekDataValid,
        trekListValid: trekListValid,
        cachedTrekCount: cachedTrekData ? cachedTrekData.length : 0,
        lastSync: lastSync ? new Date(lastSync) : null,
        cloudOnlyMode: true
      };

    } catch (error) {

      return {
        fortDataCached: false,
        fortDataValid: false,
        fortListValid: false,
        cachedFortCount: 0,
        waterfallDataCached: false,
        waterfallDataValid: false,
        waterfallListValid: false,
        cachedWaterfallCount: 0,
        trekDataCached: false,
        trekDataValid: false,
        trekListValid: false,
        cachedTrekCount: 0,
        lastSync: null,
        cloudOnlyMode: true
      };
    }
  }
}

// Export singleton instance
export default new RemoteDataService();
