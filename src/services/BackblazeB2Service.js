import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * BackblazeB2Service - Service for interacting with Backblaze B2 cloud storage
 * 
 * Features:
 * - B2 API authentication
 * - File listing and downloading
 * - Token management and refresh
 * - Error handling and retries
 */
class BackblazeB2Service {
  constructor() {
    this.applicationKeyId = process.env.EXPO_PUBLIC_BLAZEBUCKET_KEY_ID;
    this.applicationKey = process.env.EXPO_PUBLIC_BLAZEBUCKET_APP_KEY;

    if (!this.applicationKeyId || !this.applicationKey) {
      console.error('Missing Backblaze B2 credentials in environment variables');
    }

    this.buckets = {
      'trek-json': null,        // Fort data bucket (20 items)
      'waterfall-json': null,   // Waterfall data bucket (8 items)
      'treksjson': null,        // Trek data bucket (4 items)
      'caves-json': null        // Cave data bucket (5 items)
    };

    // API endpoints
    this.authUrl = 'https://api.backblazeb2.com/b2api/v2/b2_authorize_account';
    this.apiUrl = null; // Will be set after authentication
    this.downloadUrl = null; // Will be set after authentication

    // Authentication tokens
    this.authToken = null;
    this.tokenExpiry = null;
    
    // Cache keys
    this.AUTH_CACHE_KEY = 'b2_auth_token';
    this.TOKEN_EXPIRY_KEY = 'b2_token_expiry';
    this.BUCKETS_CACHE_KEY = 'b2_buckets';
    this.API_URL_KEY = 'b2_api_url';
    this.DOWNLOAD_URL_KEY = 'b2_download_url';
  }

  /**
   * Check if we have valid cached authentication
   */
  async isAuthValid() {
    try {
      const [token, expiry, buckets, apiUrl, downloadUrl] = await Promise.all([
        AsyncStorage.getItem(this.AUTH_CACHE_KEY),
        AsyncStorage.getItem(this.TOKEN_EXPIRY_KEY),
        AsyncStorage.getItem(this.BUCKETS_CACHE_KEY),
        AsyncStorage.getItem(this.API_URL_KEY),
        AsyncStorage.getItem(this.DOWNLOAD_URL_KEY)
      ]);

      if (!token || !expiry || !buckets || !apiUrl || !downloadUrl) {
        return false;
      }

      const expiryTime = new Date(expiry);
      const now = new Date();

      // Check if token expires in the next 5 minutes (buffer time)
      const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds

      if (now.getTime() + bufferTime >= expiryTime.getTime()) {
        return false;
      }

      // Set cached values
      this.authToken = token;
      this.buckets = JSON.parse(buckets);
      this.apiUrl = apiUrl;
      this.downloadUrl = downloadUrl;
      this.tokenExpiry = expiryTime;

      return true;
    } catch (error) {
      console.error('Error checking auth validity:', error);
      return false;
    }
  }

  /**
   * Authenticate with Backblaze B2 API
   */
  async authenticate() {
    try {
      // Check if we have valid cached auth
      if (await this.isAuthValid()) {
        return true;
      }

      // Create basic auth header
      const credentials = btoa(`${this.applicationKeyId}:${this.applicationKey}`);

      const response = await fetch(this.authUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${credentials}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('B2 Auth Error Response:', errorText);
        throw new Error(`B2 Authentication failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const authData = await response.json();
      
      // Set authentication data
      this.authToken = authData.authorizationToken;
      this.apiUrl = authData.apiUrl;
      this.downloadUrl = authData.downloadUrl;

      // Set token expiry (B2 tokens are valid for 24 hours)
      this.tokenExpiry = new Date(Date.now() + 23 * 60 * 60 * 1000); // 23 hours for safety

      // Get bucket IDs for all buckets
      await this.getBucketIds();

      // Cache authentication data
      await Promise.all([
        AsyncStorage.setItem(this.AUTH_CACHE_KEY, this.authToken),
        AsyncStorage.setItem(this.TOKEN_EXPIRY_KEY, this.tokenExpiry.toISOString()),
        AsyncStorage.setItem(this.BUCKETS_CACHE_KEY, JSON.stringify(this.buckets)),
        AsyncStorage.setItem(this.API_URL_KEY, this.apiUrl),
        AsyncStorage.setItem(this.DOWNLOAD_URL_KEY, this.downloadUrl)
      ]);

      return true;

    } catch (error) {
      console.error('B2 Authentication error:', error);
      throw new Error(`Failed to authenticate with Backblaze B2: ${error.message}`);
    }
  }

  /**
   * Get bucket IDs for all configured buckets
   */
  async getBucketIds() {
    try {
      const response = await fetch(`${this.apiUrl}/b2api/v2/b2_list_buckets`, {
        method: 'POST',
        headers: {
          'Authorization': this.authToken,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          accountId: this.applicationKeyId.substring(0, 12) // First 12 chars of key ID is account ID
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to list buckets: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Find and store bucket IDs for all configured buckets
      for (const bucketName of Object.keys(this.buckets)) {
        const bucket = data.buckets.find(b => b.bucketName === bucketName);
        if (bucket) {
          this.buckets[bucketName] = bucket.bucketId;
        } else {
          console.error(`Bucket '${bucketName}' not found in available buckets`);
        }
      }


      return this.buckets;

    } catch (error) {
      console.error('Error getting bucket IDs:', error);
      throw error;
    }
  }

  /**
   * List files in the specified bucket with optional prefix
   */
  async listFiles(prefix = '', bucketName = 'trek-json', maxFileCount = 1000) {
    try {
      await this.authenticate();

      const bucketId = this.buckets[bucketName];
      if (!bucketId) {
        throw new Error(`Bucket '${bucketName}' not found or not configured`);
      }

      const response = await fetch(`${this.apiUrl}/b2api/v2/b2_list_file_names`, {
        method: 'POST',
        headers: {
          'Authorization': this.authToken,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          bucketId: bucketId,
          startFileName: prefix,
          maxFileCount: maxFileCount,
          prefix: prefix
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to list files in bucket '${bucketName}': ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data.files || [];

    } catch (error) {
      console.error(`Error listing files in bucket '${bucketName}':`, error);
      throw error;
    }
  }

  /**
   * Download a file from B2 storage
   */
  async downloadFile(fileName, bucketName = 'trek-json') {
    try {
      await this.authenticate();

      const response = await fetch(`${this.downloadUrl}/file/${bucketName}/${fileName}`, {
        method: 'GET',
        headers: {
          'Authorization': this.authToken
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to download file ${fileName} from bucket '${bucketName}': ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data;

    } catch (error) {
      console.error(`Error downloading file ${fileName} from bucket '${bucketName}':`, error);
      throw error;
    }
  }

  /**
   * Download multiple files in parallel
   */
  async downloadFiles(fileNames, bucketName = 'trek-json', maxConcurrent = 5) {
    const results = [];
    const errors = [];

    // Process files in batches to avoid overwhelming the API
    for (let i = 0; i < fileNames.length; i += maxConcurrent) {
      const batch = fileNames.slice(i, i + maxConcurrent);

      const batchPromises = batch.map(async (fileName) => {
        try {
          const data = await this.downloadFile(fileName, bucketName);
          return { fileName, data, success: true };
        } catch (error) {
          errors.push({ fileName, error: error.message });
          return { fileName, data: null, success: false, error: error.message };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return { results, errors };
  }

  /**
   * Clear cached authentication data
   */
  async clearAuthCache() {
    try {
      await Promise.all([
        AsyncStorage.removeItem(this.AUTH_CACHE_KEY),
        AsyncStorage.removeItem(this.TOKEN_EXPIRY_KEY),
        AsyncStorage.removeItem(this.BUCKETS_CACHE_KEY),
        AsyncStorage.removeItem(this.API_URL_KEY),
        AsyncStorage.removeItem(this.DOWNLOAD_URL_KEY)
      ]);

      this.authToken = null;
      this.buckets = {
        'trek-json': null,        // Fort data bucket (20 items)
        'waterfall-json': null,   // Waterfall data bucket (8 items)
        'treksjson': null,        // Trek data bucket (4 items)
        'caves-json': null        // Cave data bucket (5 items)
      };
      this.apiUrl = null;
      this.downloadUrl = null;
      this.tokenExpiry = null;

    } catch (error) {
      console.error('Error clearing auth cache:', error);
    }
  }
}

// Export singleton instance
export default new BackblazeB2Service();
