/**
 * Device Logger - Enhanced logging for debugging device-specific issues
 * Provides detailed device information and error tracking
 */

import { Platform, Dimensions } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';

// Try to import expo-device, fallback if not available
let Device = null;
try {
  Device = require('expo-device');
} catch (error) {
  console.warn('expo-device not available, using fallback device info');
}

class DeviceLogger {
  constructor() {
    this.logs = [];
    this.maxLogs = 1000;
    this.logKey = 'device_debug_logs';
    this.deviceInfo = null;
    this.initialized = false;
  }

  /**
   * Initialize the logger with device information
   */
  async initialize() {
    if (this.initialized) return;

    try {
      // Collect device information
      const { width, height } = Dimensions.get('window');
      const netInfo = await NetInfo.fetch();

      this.deviceInfo = {
        // Platform info
        platform: Platform.OS,
        version: Platform.Version,

        // Device info (Expo Device or fallback)
        deviceName: Device?.deviceName || 'Unknown',
        deviceType: Device?.deviceType || 'Unknown',
        brand: Device?.brand || 'Unknown',
        manufacturer: Device?.manufacturer || 'Unknown',
        modelName: Device?.modelName || 'Unknown',
        osName: Device?.osName || Platform.OS,
        osVersion: Device?.osVersion || Platform.Version.toString(),

        // Screen info
        screenWidth: width,
        screenHeight: height,

        // Network info
        networkType: netInfo.type,
        isConnected: netInfo.isConnected,
        isInternetReachable: netInfo.isInternetReachable,

        // App info
        isDevice: Device?.isDevice ?? true, // Assume real device if Device not available
        timestamp: new Date().toISOString(),
      };

      // Load existing logs
      await this.loadLogs();
      
      // Log initialization
      this.log('INFO', 'DeviceLogger initialized', this.deviceInfo);
      
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize DeviceLogger:', error);
    }
  }

  /**
   * Log a message with level and context
   */
  async log(level, message, context = null) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      deviceInfo: this.deviceInfo,
    };

    // Add to memory logs
    this.logs.push(logEntry);
    
    // Keep only recent logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console log for development
    if (__DEV__) {
      const logMethod = level === 'ERROR' ? console.error : 
                       level === 'WARN' ? console.warn : console.log;
      logMethod(`[${level}] ${message}`, context);
    }

    // Save to storage periodically
    if (this.logs.length % 10 === 0) {
      await this.saveLogs();
    }
  }

  /**
   * Log error with stack trace
   */
  async logError(error, context = null) {
    const errorInfo = {
      message: error.message,
      stack: error.stack,
      name: error.name,
      context,
    };

    await this.log('ERROR', 'Application Error', errorInfo);
  }

  /**
   * Log network request
   */
  async logNetworkRequest(url, method, status, error = null) {
    const requestInfo = {
      url,
      method,
      status,
      error: error ? error.message : null,
      timestamp: new Date().toISOString(),
    };

    const level = error ? 'ERROR' : status >= 400 ? 'WARN' : 'INFO';
    await this.log(level, `Network ${method} ${url}`, requestInfo);
  }

  /**
   * Log app lifecycle events
   */
  async logLifecycle(event, data = null) {
    await this.log('INFO', `Lifecycle: ${event}`, data);
  }

  /**
   * Get device summary for debugging
   */
  getDeviceSummary() {
    if (!this.deviceInfo) return 'Device info not available';

    return `${this.deviceInfo.brand} ${this.deviceInfo.modelName} - ${this.deviceInfo.osName} ${this.deviceInfo.osVersion} - ${this.deviceInfo.screenWidth}x${this.deviceInfo.screenHeight}`;
  }

  /**
   * Get recent logs
   */
  getRecentLogs(count = 50) {
    return this.logs.slice(-count);
  }

  /**
   * Get logs by level
   */
  getLogsByLevel(level) {
    return this.logs.filter(log => log.level === level);
  }

  /**
   * Export logs for debugging
   */
  async exportLogs() {
    const exportData = {
      deviceInfo: this.deviceInfo,
      logs: this.logs,
      exportTimestamp: new Date().toISOString(),
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Clear all logs
   */
  async clearLogs() {
    this.logs = [];
    await AsyncStorage.removeItem(this.logKey);
    await this.log('INFO', 'Logs cleared');
  }

  /**
   * Save logs to storage
   */
  async saveLogs() {
    try {
      const logsToSave = this.logs.slice(-500); // Save only recent logs
      await AsyncStorage.setItem(this.logKey, JSON.stringify(logsToSave));
    } catch (error) {
      console.error('Failed to save logs:', error);
    }
  }

  /**
   * Load logs from storage
   */
  async loadLogs() {
    try {
      const savedLogs = await AsyncStorage.getItem(this.logKey);
      if (savedLogs) {
        this.logs = JSON.parse(savedLogs);
      }
    } catch (error) {
      console.error('Failed to load logs:', error);
      this.logs = [];
    }
  }
}

// Create singleton instance
const deviceLogger = new DeviceLogger();

// Auto-initialize when imported
deviceLogger.initialize().catch(console.error);

export default deviceLogger;

/**
 * Convenience functions for common logging
 */
export const logError = (error, context) => deviceLogger.logError(error, context);
export const logInfo = (message, context) => deviceLogger.log('INFO', message, context);
export const logWarn = (message, context) => deviceLogger.log('WARN', message, context);
export const logNetwork = (url, method, status, error) => deviceLogger.logNetworkRequest(url, method, status, error);
export const logLifecycle = (event, data) => deviceLogger.logLifecycle(event, data);
