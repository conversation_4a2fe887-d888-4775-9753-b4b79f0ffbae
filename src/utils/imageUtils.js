/**
 * Minimal image utility - loads images exclusively from Backblaze B2 data
 */

/**
 * Get image source directly from Backblaze B2 data only
 * No fallbacks, no processing, no error handling
 */
export const getImageSource = (item) => {
  // Only use images array from Backblaze B2 data
  if (item.images && item.images.length > 0) {
    return { uri: item.images[0] };
  }

  // No fallback - return null if no images
  return null;
};