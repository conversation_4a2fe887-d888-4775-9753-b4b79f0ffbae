import React, { createContext, useContext, useState, useCallback } from 'react';
import Toast from '../components/Toast';

const ToastContext = createContext();

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

export const ToastProvider = ({ children }) => {
  const [toasts, setToasts] = useState([]);

  const showToast = useCallback((message, type = 'info', options = {}) => {
    const id = Date.now() + Math.random();
    const toast = {
      id,
      message,
      type,
      visible: true,
      duration: options.duration || 3000,
      action: options.action,
      actionText: options.actionText,
    };

    setToasts(prev => [...prev, toast]);

    // Auto remove after duration + animation time
    setTimeout(() => {
      setToasts(prev => prev.filter(t => t.id !== id));
    }, (options.duration || 3000) + 500);

    return id;
  }, []);

  const hideToast = useCallback((id) => {
    setToasts(prev => 
      prev.map(toast => 
        toast.id === id ? { ...toast, visible: false } : toast
      )
    );
  }, []);

  const showSuccess = useCallback((message, options) => {
    return showToast(message, 'success', options);
  }, [showToast]);

  const showError = useCallback((message, options) => {
    return showToast(message, 'error', options);
  }, [showToast]);

  const showWarning = useCallback((message, options) => {
    return showToast(message, 'warning', options);
  }, [showToast]);

  const showInfo = useCallback((message, options) => {
    return showToast(message, 'info', options);
  }, [showToast]);

  // Convenience methods for common error scenarios
  const showNetworkError = useCallback((retryAction) => {
    return showError(
      'Connection problem. Please check your internet connection.',
      {
        duration: 5000,
        action: retryAction,
        actionText: 'Retry',
      }
    );
  }, [showError]);

  const showDataError = useCallback((retryAction) => {
    return showError(
      'Unable to load data. Please try again.',
      {
        duration: 4000,
        action: retryAction,
        actionText: 'Retry',
      }
    );
  }, [showError]);

  const showRefreshSuccess = useCallback(() => {
    return showSuccess('Data refreshed successfully!', { duration: 2000 });
  }, [showSuccess]);

  const value = {
    showToast,
    hideToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showNetworkError,
    showDataError,
    showRefreshSuccess,
  };

  return (
    <ToastContext.Provider value={value}>
      {children}
      {toasts.map(toast => (
        <Toast
          key={toast.id}
          visible={toast.visible}
          message={toast.message}
          type={toast.type}
          duration={0} // Managed by context
          onHide={() => hideToast(toast.id)}
          action={toast.action}
          actionText={toast.actionText}
        />
      ))}
    </ToastContext.Provider>
  );
};
