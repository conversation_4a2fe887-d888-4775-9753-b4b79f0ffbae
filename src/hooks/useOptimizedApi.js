import { useState, useEffect, useCallback, useRef } from 'react';

/**
 * Hook for optimized API calls with caching, debouncing, and request deduplication
 */
export const useOptimizedApi = () => {
  const [cache, setCache] = useState(new Map());
  const requestsInFlight = useRef(new Map());
  const abortControllers = useRef(new Map());

  /**
   * Make an optimized API call with caching and deduplication
   */
  const makeRequest = useCallback(async (
    key,
    requestFn,
    options = {}
  ) => {
    const {
      cacheTime = 5 * 60 * 1000, // 5 minutes default
      forceRefresh = false,
      timeout = 10000,
    } = options;

    // Check cache first
    if (!forceRefresh && cache.has(key)) {
      const cached = cache.get(key);
      const isExpired = Date.now() - cached.timestamp > cacheTime;
      
      if (!isExpired) {
        return cached.data;
      }
    }

    // Check if request is already in flight
    if (requestsInFlight.current.has(key)) {
      return requestsInFlight.current.get(key);
    }

    // Create abort controller for this request
    const abortController = new AbortController();
    abortControllers.current.set(key, abortController);

    // Create the request promise
    const requestPromise = (async () => {
      try {
        // Add timeout
        const timeoutId = setTimeout(() => {
          abortController.abort();
        }, timeout);

        const result = await requestFn(abortController.signal);
        
        clearTimeout(timeoutId);

        // Cache the result
        setCache(prev => new Map(prev).set(key, {
          data: result,
          timestamp: Date.now(),
        }));

        return result;
      } catch (error) {
        if (error.name === 'AbortError') {
          throw new Error('Request was cancelled');
        }
        throw error;
      } finally {
        // Clean up
        requestsInFlight.current.delete(key);
        abortControllers.current.delete(key);
      }
    })();

    // Store the promise to prevent duplicate requests
    requestsInFlight.current.set(key, requestPromise);

    return requestPromise;
  }, [cache]);

  /**
   * Cancel a specific request
   */
  const cancelRequest = useCallback((key) => {
    const controller = abortControllers.current.get(key);
    if (controller) {
      controller.abort();
    }
  }, []);

  /**
   * Cancel all pending requests
   */
  const cancelAllRequests = useCallback(() => {
    abortControllers.current.forEach(controller => {
      controller.abort();
    });
    abortControllers.current.clear();
    requestsInFlight.current.clear();
  }, []);

  /**
   * Clear cache for a specific key or all cache
   */
  const clearCache = useCallback((key = null) => {
    if (key) {
      setCache(prev => {
        const newCache = new Map(prev);
        newCache.delete(key);
        return newCache;
      });
    } else {
      setCache(new Map());
    }
  }, []);

  /**
   * Get cached data without making a request
   */
  const getCachedData = useCallback((key) => {
    const cached = cache.get(key);
    return cached ? cached.data : null;
  }, [cache]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cancelAllRequests();
    };
  }, [cancelAllRequests]);

  return {
    makeRequest,
    cancelRequest,
    cancelAllRequests,
    clearCache,
    getCachedData,
    cacheSize: cache.size,
  };
};

/**
 * Hook for debounced API calls (useful for search)
 */
export const useDebouncedApi = (delay = 300) => {
  const [debouncedValue, setDebouncedValue] = useState('');
  const timeoutRef = useRef(null);

  const setDebounced = useCallback((value) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
  }, [delay]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return [debouncedValue, setDebounced];
};

/**
 * Hook for batch API requests
 */
export const useBatchApi = () => {
  const batchQueue = useRef([]);
  const batchTimeoutRef = useRef(null);

  const addToBatch = useCallback((request, batchDelay = 100) => {
    batchQueue.current.push(request);

    if (batchTimeoutRef.current) {
      clearTimeout(batchTimeoutRef.current);
    }

    batchTimeoutRef.current = setTimeout(async () => {
      const requests = [...batchQueue.current];
      batchQueue.current = [];

      // Execute all requests in parallel
      try {
        await Promise.allSettled(requests.map(req => req()));
      } catch (error) {
        // Handle batch errors
      }
    }, batchDelay);
  }, []);

  return { addToBatch };
};

export default useOptimizedApi;
