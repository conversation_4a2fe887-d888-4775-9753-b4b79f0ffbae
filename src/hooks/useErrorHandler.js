import { useState, useCallback } from 'react';
import { Alert } from 'react-native';

/**
 * Custom hook for standardized error handling
 * Provides consistent error handling patterns across the app
 */
export const useErrorHandler = () => {
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  /**
   * Handle async operations with standardized error handling
   */
  const handleAsync = useCallback(async (
    asyncOperation,
    options = {}
  ) => {
    const {
      showAlert = false,
      alertTitle = 'Error',
      alertMessage = 'An unexpected error occurred. Please try again.',
      onError = null,
      onSuccess = null,
      setLoadingState = true,
    } = options;

    if (setLoadingState) {
      setLoading(true);
    }
    setError(null);

    try {
      const result = await asyncOperation();
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (err) {
      const errorMessage = err.message || 'Unknown error occurred';
      setError(errorMessage);

      // Show alert if requested
      if (showAlert) {
        Alert.alert(alertTitle, alertMessage);
      }

      // Call custom error handler if provided
      if (onError) {
        onError(err);
      }

      // In development, log the error for debugging
      if (__DEV__) {
        console.error('useErrorHandler caught error:', err);
      }

      return null;
    } finally {
      if (setLoadingState) {
        setLoading(false);
      }
    }
  }, []);

  /**
   * Clear current error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Handle network errors specifically
   */
  const handleNetworkError = useCallback((error) => {
    let userMessage = 'Network error occurred. Please check your connection.';
    
    if (error.message?.includes('timeout')) {
      userMessage = 'Request timed out. Please try again.';
    } else if (error.message?.includes('Network request failed')) {
      userMessage = 'No internet connection. Please check your network.';
    } else if (error.message?.includes('404')) {
      userMessage = 'Requested resource not found.';
    } else if (error.message?.includes('500')) {
      userMessage = 'Server error. Please try again later.';
    }

    setError(userMessage);
    return userMessage;
  }, []);

  /**
   * Handle API errors with user-friendly messages
   */
  const handleApiError = useCallback((error, context = '') => {
    let userMessage = 'Something went wrong. Please try again.';

    if (error.response) {
      // Server responded with error status
      const status = error.response.status;
      switch (status) {
        case 400:
          userMessage = 'Invalid request. Please check your input.';
          break;
        case 401:
          userMessage = 'Authentication required. Please log in.';
          break;
        case 403:
          userMessage = 'Access denied. You don\'t have permission.';
          break;
        case 404:
          userMessage = 'Resource not found.';
          break;
        case 429:
          userMessage = 'Too many requests. Please wait and try again.';
          break;
        case 500:
          userMessage = 'Server error. Please try again later.';
          break;
        default:
          userMessage = `Server error (${status}). Please try again.`;
      }
    } else if (error.request) {
      // Network error
      userMessage = handleNetworkError(error);
    }

    if (context) {
      userMessage = `${context}: ${userMessage}`;
    }

    setError(userMessage);
    return userMessage;
  }, [handleNetworkError]);

  return {
    error,
    loading,
    handleAsync,
    clearError,
    handleNetworkError,
    handleApiError,
  };
};

/**
 * Hook for handling form validation errors
 */
export const useFormErrorHandler = () => {
  const [fieldErrors, setFieldErrors] = useState({});

  const setFieldError = useCallback((field, message) => {
    setFieldErrors(prev => ({
      ...prev,
      [field]: message,
    }));
  }, []);

  const clearFieldError = useCallback((field) => {
    setFieldErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const clearAllErrors = useCallback(() => {
    setFieldErrors({});
  }, []);

  const hasErrors = Object.keys(fieldErrors).length > 0;

  return {
    fieldErrors,
    setFieldError,
    clearFieldError,
    clearAllErrors,
    hasErrors,
  };
};

export default useErrorHandler;
