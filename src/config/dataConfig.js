/**
 * Data Service Configuration
 * 
 * This file contains configuration for the remote data service.
 * Update the BASE_URL with your actual GitHub Pages URL after setting up the repository.
 */

export const DATA_CONFIG = {
  // PRODUCTION: Using local data only for production release
  // Remote data service disabled until proper backend is configured
  BASE_URL: null, // Disabled for production

  // Production configuration - use local data only
  USE_LOCAL_FALLBACK: true,
  
  // Cache settings
  CACHE_DURATION: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
  
  // Retry settings
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second
  
  // Timeout settings
  REQUEST_TIMEOUT: 10000, // 10 seconds
  
  // Cache keys
  CACHE_KEYS: {
    METADATA: 'trek_metadata',
    ALL_TREKS: 'all_treks',
    FORTS: 'forts_data',
    WATERFALLS: 'waterfalls_data',
    CAVES: 'caves_data',
    TREKS: 'treks_data',
    FEATURED: 'featured_data',
    LAST_UPDATE: 'data_last_update'
  },
  
  // API endpoints
  ENDPOINTS: {
    ALL: 'all.json',
    FORTS: 'forts.json',
    WATERFALLS: 'waterfalls.json',
    CAVES: 'caves.json',
    TREKS: 'treks.json',
    FEATURED: 'featured.json',
    METADATA: 'metadata.json',
    EASY: 'easy.json',
    MODERATE: 'moderate.json',
    DIFFICULT: 'difficult.json'
  }
};

/**
 * Environment-specific configuration
 */
export const getDataConfig = () => {
  // You can add environment-specific logic here
  const isDevelopment = __DEV__;
  
  if (isDevelopment) {
    return {
      ...DATA_CONFIG,
      // Use local data more aggressively in development
      USE_LOCAL_FALLBACK: true,
      CACHE_DURATION: 5 * 60 * 1000, // 5 minutes in development
    };
  }
  
  return DATA_CONFIG;
};

/**
 * Validation helpers
 */
export const validateDataConfig = () => {
  const config = getDataConfig();

  // Production: Remote data service disabled, using local data only
  if (!config.BASE_URL) {
    return false; // Use local data
  }

  if (config.BASE_URL.includes('your-username')) {
    return false; // Invalid placeholder URL
  }

  return true;
};
