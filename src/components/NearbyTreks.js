import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Dimensions,
  Platform,
} from 'react-native';
import LocationService from '../services/LocationService';
import { COLORS, SPACING, BORDER_RADIUS, SHADOWS, createTextStyle } from '../utils/constants';
import { getImageSource } from '../utils/imageUtils';

const { width } = Dimensions.get('window');

// Cache for nearby treks to avoid repeated calculations
let nearbyTreksCache = {
  data: null,
  locationStatus: null,
  userLocationName: null,
  timestamp: null,
  treksHash: null,
  excludeHash: null,
  locationHash: null,
};

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * NearbyTreks Component
 *
 * Features:
 * - Shows treks near user's current location
 * - Displays distance from user location
 * - <PERSON>les location permissions gracefully
 * - Provides fallback when location is unavailable
 * - Refreshable content
 * - Caches results to avoid repeated API calls
 */
const NearbyTreks = ({ treks = [], navigation, maxDistance = 100, limit = 6, excludeTreks = [] }) => {
  const [nearbyTreks, setNearbyTreks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [locationStatus, setLocationStatus] = useState({
    available: false,
    message: 'Tap "Find Treks Near Me" to discover nearby adventures',
    showButton: true
  });
  const [userLocationName, setUserLocationName] = useState('Your Location');
  const [hasInitialized, setHasInitialized] = useState(false);

  // Remove automatic initialization - now user-triggered only
  // useEffect(() => {
  //   if (!initializationRef.current) {
  //     initializationRef.current = true;
  //     initializeLocation();
  //   }
  // }, []);

  // Check if treks data changed and invalidate cache if needed
  useEffect(() => {
    const treksHash = JSON.stringify(treks.map(t => ({ id: t.id, coordinates: t.coordinates })));
    if (nearbyTreksCache.treksHash && nearbyTreksCache.treksHash !== treksHash) {
      nearbyTreksCache = { data: null, locationStatus: null, userLocationName: null, timestamp: null, treksHash: null, locationHash: null };
      // Only re-initialize if user has already triggered search
      if (hasInitialized) {
        initializeLocation();
      }
    }
  }, [treks, hasInitialized]);

  // User-triggered search function
  const handleFindNearbyTreks = () => {
    setHasInitialized(true);
    initializeLocation();
  };

  const initializeLocation = async (forceRefresh = false) => {
    setLoading(true);
    setLocationStatus({
      available: false,
      message: 'Finding treks near you...',
      showButton: false
    });

    try {
      // Create hashes for cache validation
      const treksHash = JSON.stringify(treks.map(t => ({ id: t.id, coordinates: t.coordinates })));
      const excludeHash = JSON.stringify(excludeTreks.map(t => t.id).sort()); // Include exclude list in cache
      const locationHash = LocationService.userLocation ?
        JSON.stringify({ lat: LocationService.userLocation.latitude, lng: LocationService.userLocation.longitude, isReal: LocationService.userLocation.isReal }) :
        null;

      // Check if we can use cached data
      if (!forceRefresh && nearbyTreksCache.data && nearbyTreksCache.timestamp) {
        const cacheAge = Date.now() - nearbyTreksCache.timestamp;
        const isCacheValid = cacheAge < CACHE_DURATION &&
                           nearbyTreksCache.treksHash === treksHash &&
                           nearbyTreksCache.excludeHash === excludeHash &&
                           nearbyTreksCache.locationHash === locationHash;

        if (isCacheValid) {
          setNearbyTreks(nearbyTreksCache.data);
          setLocationStatus(nearbyTreksCache.locationStatus);
          setUserLocationName(nearbyTreksCache.userLocationName);
          setLoading(false);
          return;
        }
      }


      // Initialize location service
      const hasPermission = await LocationService.initialize();

      if (hasPermission) {
        // Get user location name
        const locationName = await LocationService.getCurrentLocationName();
        setUserLocationName(locationName);

        // Find nearby treks and exclude featured destinations
        const excludeIds = excludeTreks.map(trek => trek.id);
        const filteredTreks = treks.filter(trek => !excludeIds.includes(trek.id));
        const nearby = LocationService.findNearbyTreks(filteredTreks, maxDistance, limit);
        setNearbyTreks(nearby);

        // Check if distances are calculated from real or fallback location
        const isUsingFallback = LocationService.userLocation && !LocationService.userLocation.isReal;
        const statusMessage = isUsingFallback
          ? `Found ${nearby.length} treks (distances from Pune area)`
          : `Found ${nearby.length} treks near you`;

        const newLocationStatus = {
          available: true,
          message: statusMessage,
          isUsingFallback,
        };

        setLocationStatus(newLocationStatus);

        // Cache the results
        nearbyTreksCache = {
          data: nearby,
          locationStatus: newLocationStatus,
          userLocationName: locationName,
          timestamp: Date.now(),
          treksHash,
          excludeHash,
          locationHash: LocationService.userLocation ?
            JSON.stringify({ lat: LocationService.userLocation.latitude, lng: LocationService.userLocation.longitude, isReal: LocationService.userLocation.isReal }) :
            null,
        };
      } else {
        // No permission - show featured/popular treks instead (excluding already featured ones)
        const excludeIds = excludeTreks.map(trek => trek.id);
        const featuredTreks = treks
          .filter(trek => !excludeIds.includes(trek.id)) // Exclude already featured treks
          .filter(trek => trek.featured || trek.rating >= 4.0)
          .sort((a, b) => (b.rating || 0) - (a.rating || 0))
          .slice(0, limit)
          .map(trek => ({ ...trek, distance: null, distanceText: null, showAsFeatured: true }));

        const newLocationStatus = {
          available: false,
          message: 'Enable location to see nearby treks',
          action: 'Enable Location',
        };

        setNearbyTreks(featuredTreks);
        setLocationStatus(newLocationStatus);

        // Cache the fallback results
        nearbyTreksCache = {
          data: featuredTreks,
          locationStatus: newLocationStatus,
          userLocationName: 'Your Location',
          timestamp: Date.now(),
          treksHash,
          excludeHash,
          locationHash: null,
        };
      }
    } catch (error) {
      setLocationStatus({
        available: false,
        message: 'Unable to get location',
        action: 'Retry',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLocationAction = () => {
    if (locationStatus.action === 'Enable Location') {
      Alert.alert(
        'Location Permission',
        'To show treks near you, please enable location permission in your device settings.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Retry', onPress: () => initializeLocation(true) },
        ]
      );
    } else if (locationStatus.action === 'Retry') {
      initializeLocation(true);
    }
  };

  const handleTrekPress = (trek) => {
    navigation.navigate('TrekDetails', { trek });
  };

  const handleViewAllNearby = () => {
    // Navigate to trek list with location filter and nearby treks data
    navigation.navigate('TrekList', {
      category: 'nearby',
      userLocation: LocationService.userLocation,
      title: `Near ${userLocationName}`,
      nearbyTreks: nearbyTreks,
      maxDistance: maxDistance,
      allTreks: treks
    });
  };

  const getTrekImageSource = (trek) => {
    return getImageSource(trek);
  };

  const renderTrekCard = ({ item: trek }) => (
    <TouchableOpacity
      style={styles.trekCard}
      onPress={() => handleTrekPress(trek)}
      activeOpacity={0.8}
    >
      {getTrekImageSource(trek) && (
        <Image
          source={getTrekImageSource(trek)}
          style={styles.trekImage}
          resizeMode="cover"
        />
      )}
      <View style={styles.trekContent}>
        <View style={styles.trekHeader}>
          <Text style={styles.trekName} numberOfLines={2}>
            {trek.name}
          </Text>
          {/* Only show distance container if we have distance data */}
          {trek.distanceText && (
            <View style={styles.distanceContainer}>
              <Text style={styles.distanceText}>
                {trek.distanceText}
              </Text>
            </View>
          )}
          {/* Show rating for featured treks when no location */}
          {trek.showAsFeatured && (
            <View style={styles.ratingContainer}>
              <Text style={styles.ratingText}>⭐ {trek.rating}</Text>
            </View>
          )}
        </View>

        <Text style={styles.trekLocation} numberOfLines={1}>
          📍 {trek.location}
        </Text>

        <View style={styles.trekFooter}>
          <View style={styles.difficultyContainer}>
            <Text style={styles.difficultyText}>{trek.difficulty}</Text>
          </View>
          {trek.duration && (
            <Text style={styles.durationText}>{trek.duration}</Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <View style={styles.titleContainer}>
        <Text style={styles.sectionTitle}>
          {locationStatus.available ? `Near ${userLocationName}` : 'Discover Nearby Treks'}
        </Text>
        <View style={styles.headerActions}>
          {/* Show "Find Treks Near Me" button when not initialized */}
          {locationStatus.showButton && !hasInitialized && (
            <TouchableOpacity onPress={handleFindNearbyTreks} style={styles.findNearbyButton}>
              <Text style={styles.findNearbyButtonText}>Find Treks Near Me</Text>
            </TouchableOpacity>
          )}
          {locationStatus.available && nearbyTreks.length > 0 && (
            <TouchableOpacity onPress={handleViewAllNearby} style={styles.viewAllButton}>
              <Text style={styles.viewAllButtonText}>View All</Text>
            </TouchableOpacity>
          )}
          {/* Refresh location button for accurate distances */}
          {locationStatus.available && (
            <TouchableOpacity onPress={() => initializeLocation(true)} style={styles.refreshButton}>
              <Text style={styles.refreshButtonText}>📍</Text>
            </TouchableOpacity>
          )}
          {locationStatus.action && (
            <TouchableOpacity onPress={handleLocationAction} style={styles.actionButton}>
              <Text style={styles.actionButtonText}>{locationStatus.action}</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      <Text style={styles.statusText}>{locationStatus.message}</Text>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>🗺️</Text>
      <Text style={styles.emptyTitle}>No nearby treks found</Text>
      <Text style={styles.emptySubtitle}>
        Try expanding your search radius or check your location settings
      </Text>
      <TouchableOpacity style={styles.retryButton} onPress={() => initializeLocation(true)}>
        <Text style={styles.retryButtonText}>Retry</Text>
      </TouchableOpacity>
    </View>
  );

  // Show loading only when actively searching
  if (loading) {
    return (
      <View style={styles.container}>
        {renderHeader()}
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Finding treks near you...</Text>
        </View>
      </View>
    );
  }

  // Show initial state with featured treks when not initialized
  if (!hasInitialized) {
    // Exclude already featured treks to avoid duplication
    const excludeIds = excludeTreks.map(trek => trek.id);
    const featuredTreks = treks
      .filter(trek => !excludeIds.includes(trek.id)) // Exclude already featured treks
      .filter(trek => trek.featured || trek.rating >= 4.0)
      .sort((a, b) => (b.rating || 0) - (a.rating || 0))
      .slice(0, limit)
      .map(trek => ({ ...trek, distance: null, distanceText: null, showAsFeatured: true }));

    return (
      <View style={styles.container}>
        {renderHeader()}
        {featuredTreks.length > 0 ? (
          <FlatList
            data={featuredTreks}
            renderItem={renderTrekCard}
            keyExtractor={(item, index) => `featured-${item.id}-${index}`}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.treksList}
            decelerationRate="fast"
            snapToInterval={(Platform.OS === 'android' ? Math.min(width * 0.63, 250) : Math.min(width * 0.65, 260)) + SPACING.lg}
            snapToAlignment="start"
          />
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyIcon}>🏔️</Text>
            <Text style={styles.emptyTitle}>Discover Amazing Treks</Text>
            <Text style={styles.emptySubtitle}>
              Find treks near your location or explore featured destinations
            </Text>
          </View>
        )}
      </View>
    );
  }

  // Show empty state when initialized but no results
  if (nearbyTreks.length === 0) {
    return (
      <View style={styles.container}>
        {renderHeader()}
        {renderEmptyState()}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {renderHeader()}
      <FlatList
        data={nearbyTreks}
        renderItem={renderTrekCard}
        keyExtractor={(item, index) => `nearby-${item.id}-${index}`}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.treksList}
        decelerationRate="fast"
        snapToInterval={(Platform.OS === 'android' ? Math.min(width * 0.63, 250) : Math.min(width * 0.65, 260)) + SPACING.lg}
        snapToAlignment="start"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Platform.OS === 'android' ? SPACING.xxl : SPACING.xl, // Extra spacing on Android
  },
  headerContainer: {
    paddingHorizontal: SPACING.xl,
    marginBottom: SPACING.lg,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  sectionTitle: {
    ...createTextStyle(18, 'bold'),
    color: COLORS.text,
    flex: 1,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  viewAllButton: {
    backgroundColor: COLORS.backgroundSecondary,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
  },
  viewAllButtonText: {
    ...createTextStyle(12, 'medium'),
    color: COLORS.text,
  },
  actionButton: {
    backgroundColor: COLORS.primary + '15',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
  },
  actionButtonText: {
    ...createTextStyle(12, 'medium'),
    color: COLORS.primary,
  },
  findNearbyButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    ...SHADOWS.small,
  },
  findNearbyButtonText: {
    ...createTextStyle(14, 'bold'),
    color: COLORS.white,
  },
  refreshButton: {
    backgroundColor: COLORS.success + '15',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    minWidth: 36,
    alignItems: 'center',
  },
  refreshButtonText: {
    fontSize: 14,
  },
  statusText: {
    ...createTextStyle(12, 'regular'),
    color: COLORS.textSecondary,
  },
  treksList: {
    paddingHorizontal: SPACING.xl, // Add padding on both sides
    paddingRight: SPACING.xl, // Ensure right padding for last card
    paddingBottom: Platform.OS === 'android' ? SPACING.lg : 0, // Extra bottom padding on Android
  },
  trekCard: {
    width: Platform.OS === 'android' ? Math.min(width * 0.63, 250) : Math.min(width * 0.65, 260), // Slightly smaller on Android for better fit
    marginRight: SPACING.lg,
    backgroundColor: COLORS.backgroundCard,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
    ...SHADOWS.medium,
  },
  trekImage: {
    width: '100%',
    height: 120,
    backgroundColor: COLORS.backgroundSecondary,
  },
  trekContent: {
    padding: SPACING.lg,
  },
  trekHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.sm,
  },
  trekName: {
    ...createTextStyle(15, 'bold'),
    color: COLORS.text,
    flex: 1,
    marginRight: SPACING.sm,
    lineHeight: 20,
  },
  distanceContainer: {
    backgroundColor: COLORS.primary + '15',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
  },
  distanceText: {
    ...createTextStyle(11, 'medium'),
    color: COLORS.primary,
  },
  trekLocation: {
    ...createTextStyle(12, 'regular'),
    color: COLORS.textSecondary,
    marginBottom: SPACING.md,
  },
  trekFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  difficultyContainer: {
    backgroundColor: COLORS.success + '15',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
  },
  difficultyText: {
    ...createTextStyle(11, 'medium'),
    color: COLORS.success,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    ...createTextStyle(11, 'medium'),
    color: COLORS.text,
  },
  durationText: {
    ...createTextStyle(11, 'medium'),
    color: COLORS.textSecondary,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
  },
  loadingText: {
    ...createTextStyle(14, 'regular'),
    color: COLORS.textSecondary,
    marginTop: SPACING.md,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
    paddingHorizontal: SPACING.xl,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: SPACING.md,
  },
  emptyTitle: {
    ...createTextStyle(16, 'bold'),
    color: COLORS.text,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  emptySubtitle: {
    ...createTextStyle(14, 'regular'),
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: SPACING.lg,
  },
  retryButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  retryButtonText: {
    ...createTextStyle(14, 'medium'),
    color: COLORS.textInverse,
  },
});

// Memoize component to prevent unnecessary re-renders
export default React.memo(NearbyTreks, (prevProps, nextProps) => {
  // Custom comparison for performance optimization
  return (
    prevProps.treks?.length === nextProps.treks?.length &&
    prevProps.maxDistance === nextProps.maxDistance &&
    prevProps.limit === nextProps.limit &&
    prevProps.excludeTreks?.length === nextProps.excludeTreks?.length
  );
});
