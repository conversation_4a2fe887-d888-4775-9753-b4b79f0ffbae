import React, { useRef, useEffect, useState, Component } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  Platform,
  Text,
  TouchableOpacity,
} from 'react-native';
import * as Location from 'expo-location';
import { MAPBOX_CONFIG, LOCATION_CATEGORIES } from '../config/mapbox';
import { COLORS, SHADOWS } from '../utils/constants';

// Error boundary and warning suppression for Mapbox Fragment issues
let originalConsoleWarn = null;
let originalConsoleError = null;

const suppressMapboxWarnings = () => {
  // Store original functions if not already stored
  if (!originalConsoleWarn) {
    originalConsoleWarn = console.warn;
    originalConsoleError = console.error;
  }

  console.warn = (...args) => {
    const message = args[0];
    if (message && typeof message === 'string') {
      // Comprehensive Fragment error patterns
      const fragmentPatterns = [
        'Invalid prop `sourceID` supplied to `React.Fragment`',
        'React.Fragment can only have `key` and `children` props',
        'sourceID',
        'Invalid prop',
        '<PERSON><PERSON> does not recognize the `sourceID` prop',
        'Warning: React.Fragment'
      ];

      if (fragmentPatterns.some(pattern => message.includes(pattern))) {
        return;
      }
    }
    originalConsoleWarn.apply(console, args);
  };

  console.error = (...args) => {
    const message = args[0];
    if (message && typeof message === 'string') {
      // Comprehensive Fragment error patterns
      const fragmentPatterns = [
        'Invalid prop `sourceID` supplied to `React.Fragment`',
        'React.Fragment can only have `key` and `children` props',
        'sourceID',
        'Invalid prop',
        'React does not recognize the `sourceID` prop',
        'Error: React.Fragment'
      ];

      if (fragmentPatterns.some(pattern => message.includes(pattern))) {
        return;
      }
    }
    originalConsoleError.apply(console, args);
  };
};

// Function to restore original console functions (for cleanup)
const restoreConsole = () => {
  if (originalConsoleWarn) {
    originalConsoleWarn = null;
    originalConsoleError = null;
  }
};

// Apply the suppression when the component loads
suppressMapboxWarnings();

// Error Boundary Component for Mapbox Fragment Issues
class MapboxErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    // Check if this is a Fragment-related error
    if (error && error.message &&
        (error.message.includes('sourceID') ||
         error.message.includes('Fragment') ||
         error.message.includes('Invalid prop'))) {
      return { hasError: true, error };
    }
    // Let other errors bubble up
    throw error;
  }

  componentDidCatch(error, errorInfo) {
    if (error && error.message &&
        (error.message.includes('sourceID') ||
         error.message.includes('Fragment'))) {
      // Silently handle Fragment errors
    }
  }

  render() {
    if (this.state.hasError) {
      // Render fallback UI for Fragment errors
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Map loading...</Text>
          <Text style={styles.errorSubtext}>Initializing Mapbox components</Text>
        </View>
      );
    }

    return this.props.children;
  }
}



// Category-specific icons for map markers
const getCategoryIcon = (category) => {
  const icons = {
    fort: '🏰',      // Fort/Castle icon
    waterfall: '💧', // Waterfall icon
    trek: '🥾',      // Hiking boot icon
    cave: '🕳️',      // Cave icon
  };
  return icons[category] || '📍'; // Default location pin
};

// Safely import Mapbox with error handling
let Mapbox = null;
let isMapboxAvailable = false;

try {
  Mapbox = require('@rnmapbox/maps').default;
  if (Mapbox && Mapbox.setAccessToken) {
    Mapbox.setAccessToken(MAPBOX_CONFIG.accessToken);
    isMapboxAvailable = true;
  }
} catch (error) {
  isMapboxAvailable = false;
}

const MapboxMapView = ({
  locations = [],
  selectedLocation = null,
  onLocationPress = () => {},
  onMapPress = () => {},
  showUserLocation = true,
  style = {},
  initialCenter = { latitude: 18.5204, longitude: 73.8567 }, // Pune, Maharashtra
  initialZoom = 10,
  mapStyle = MAPBOX_CONFIG.defaultStyle,
  bounds = null,
  activeFilter = 'all',
}) => {
  const mapRef = useRef(null);
  const cameraRef = useRef(null);
  const [userLocation, setUserLocation] = useState(null);
  const [locationPermission, setLocationPermission] = useState(false);


  // Request location permissions and setup cleanup
  useEffect(() => {
    requestLocationPermission();

    // Cleanup function to restore console on unmount
    return () => {
      restoreConsole();
    };
  }, []);

  // Handle bounds changes when filter changes
  useEffect(() => {
    if (bounds && cameraRef.current && isMapboxAvailable) {
      const adjustCamera = async () => {
        try {
          if (bounds.bounds) {
            // Multiple locations - fit to bounds
            await cameraRef.current.fitBounds(
              bounds.bounds[0], // Southwest
              bounds.bounds[1], // Northeast
              [50, 50, 50, 50], // Padding: top, right, bottom, left
              1000 // Animation duration
            );
          } else if (bounds.center && bounds.zoom) {
            // Single location - center with zoom
            await cameraRef.current.setCamera({
              centerCoordinate: bounds.center,
              zoomLevel: bounds.zoom,
              animationDuration: 1000,
            });
          }
        } catch (error) {
          console.warn('MapboxMapView: Error adjusting camera:', error);
        }
      };

      // Small delay to ensure map is ready
      const timeoutId = setTimeout(adjustCamera, 500);
      return () => clearTimeout(timeoutId);
    }
  }, [bounds, activeFilter]);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        setLocationPermission(true);
        getCurrentLocation();
      } else {
        Alert.alert(
          'Location Permission',
          'Location permission is required to show your position on the map.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
    }
  };

  const getCurrentLocation = async () => {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });
      setUserLocation({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });
    } catch (error) {
    }
  };



  const handleMarkerPress = (location) => {
    onLocationPress(location);
    // Center map on selected location
    if (cameraRef.current) {
      cameraRef.current.setCamera({
        centerCoordinate: [location.coordinates.longitude, location.coordinates.latitude],
        zoomLevel: 14,
        animationDuration: 1000,
      });
    }
  };

  const handleMapPress = (feature) => {
    try {
      onMapPress(feature);
    } catch (error) {
      if (error.message && (error.message.includes('Fragment') || error.message.includes('sourceID'))) {
        return;
      }
      throw error;
    }
  };

  // Create marker for location
  const renderLocationMarker = (location, index) => {
    // Validate location data
    if (!location || !location.coordinates ||
        typeof location.coordinates.latitude !== 'number' ||
        typeof location.coordinates.longitude !== 'number') {
      return null;
    }

    // Use mapCategory if available, fallback to category
    const categoryKey = location.mapCategory || location.category || 'trek';
    const category = LOCATION_CATEGORIES[categoryKey] || LOCATION_CATEGORIES.trek;
    const isSelected = selectedLocation?.id === location.id;
    const categoryIcon = getCategoryIcon(categoryKey);

    // Create unique key and id combining category, id, and index to prevent duplicates
    const uniqueKey = `${categoryKey}-${location.id}-${index}`;
    const uniqueId = `marker-${categoryKey}-${location.id}-${index}`;

    // Debug log for trek markers (removed for production)

    return (
      <Mapbox.PointAnnotation
        key={uniqueKey}
        id={uniqueId}
        coordinate={[location.coordinates.longitude, location.coordinates.latitude]}
        onSelected={() => handleMarkerPress(location)}
      >
        <View style={[
          styles.markerContainer,
          { backgroundColor: category.color },
          isSelected && styles.selectedMarker,
        ]}>
          {isSelected && (
            <View style={[
              styles.selectionRing,
              { borderColor: category.color }
            ]} />
          )}
          <Text style={[
            styles.markerIcon,
            isSelected && styles.selectedMarkerIcon
          ]}>
            {categoryIcon}
          </Text>
        </View>

        <Mapbox.Callout title={location.name} />
      </Mapbox.PointAnnotation>
    );
  };



  // Render fallback when Mapbox is not available
  if (!isMapboxAvailable || !Mapbox) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.fallbackContainer}>
          <Text style={styles.fallbackTitle}>📱 Development Build Required</Text>
          <Text style={styles.fallbackText}>
            Mapbox maps require a development build.{'\n\n'}
            Run: expo run:android or expo run:ios{'\n\n'}
            Currently using Google Maps fallback.
          </Text>
          <TouchableOpacity
            style={styles.fallbackButton}
            onPress={() => Alert.alert(
              'Mapbox Maps',
              'To use Mapbox maps:\n\n1. Create a development build:\n   expo run:android\n   expo run:ios\n\n2. Install on device/simulator\n3. Toggle to Mapbox provider\n\nCurrently using Google Maps as fallback.',
              [{ text: 'OK' }]
            )}
          >
            <Text style={styles.fallbackButtonText}>Learn More</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <MapboxErrorBoundary>
      <View style={[styles.container, style]}>
        <Mapbox.MapView
          ref={mapRef}
          style={styles.map}
          styleURL={mapStyle}
          onPress={handleMapPress}
          compassEnabled={true}
          scaleBarEnabled={true}
          attributionEnabled={false}
          logoEnabled={false}
        >
        <Mapbox.Camera
          ref={cameraRef}
          centerCoordinate={[initialCenter.longitude, initialCenter.latitude]}
          zoomLevel={initialZoom}
          animationMode="flyTo"
          animationDuration={1000}
        />

        {/* User Location */}
        {showUserLocation && locationPermission && (
          <Mapbox.UserLocation
            visible={true}
            showsUserHeadingIndicator={true}
            minDisplacement={10}
          />
        )}

        {/* Location markers */}
        {Array.isArray(locations) && locations
          .filter(location => location && location.coordinates)
          .map((location, index) => renderLocationMarker(location, index))
          .filter(marker => marker !== null)}
      </Mapbox.MapView>


    </View>
    </MapboxErrorBoundary>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  map: {
    flex: 1,
  },
  markerContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: COLORS.background,
    ...SHADOWS.medium,
  },
  selectedMarker: {
    transform: [{ scale: 1.3 }],
    backgroundColor: COLORS.white,
    borderColor: COLORS.white,
    borderWidth: 3,
    shadowColor: COLORS.text,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 15,
  },
  selectionRing: {
    position: 'absolute',
    width: 56,
    height: 56,
    borderRadius: 28,
    borderWidth: 3,
    top: -8,
    left: -8,
    backgroundColor: 'transparent',
  },
  markerIcon: {
    fontSize: 20,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  selectedMarkerIcon: {
    fontSize: 24,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },

  fallbackContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: COLORS.backgroundSecondary,
  },
  fallbackTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: 16,
  },
  fallbackText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 20,
  },
  fallbackButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    ...SHADOWS.small,
  },
  fallbackButtonText: {
    color: COLORS.white,
    fontSize: 14,
    fontWeight: '600',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 8,
  },
  errorSubtext: {
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
});

export default MapboxMapView;
