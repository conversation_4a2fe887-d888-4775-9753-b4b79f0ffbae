import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  Image,
  Dimensions,
  Platform,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { COLORS, CATEGORIES, CATEGORY_COLORS, DIFFICULTY_COLORS, DIFFICULTY_LEVELS, SPACING, BORDER_RADIUS, SHADOWS } from '../utils/constants';

const { width: screenWidth } = Dimensions.get('window');

const LocationDetailsModal = ({
  visible,
  location,
  onClose,
  onNavigate,
  onViewDetails,
}) => {
  const insets = useSafeAreaInsets();

  if (!location) return null;

  const categoryInfo = CATEGORY_COLORS[location.category] || CATEGORY_COLORS[CATEGORIES.TREK];
  const difficultyInfo = DIFFICULTY_COLORS[location.difficulty] || DIFFICULTY_COLORS[DIFFICULTY_LEVELS.EASY];
  const difficultyColor = difficultyInfo?.color || COLORS.text;

  const handleNavigate = () => {
    onNavigate(location);
    onClose();
  };

  const handleViewDetails = () => {
    onViewDetails(location);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <TouchableOpacity 
          style={styles.overlayTouch} 
          activeOpacity={1} 
          onPress={onClose}
        />
        
        <View style={[styles.modalContainer, { paddingBottom: Math.max(insets.bottom, SPACING.lg) }]}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.dragHandle} />
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => {
                onClose();
              }}
              activeOpacity={0.7}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }} // Increase touch area
            >
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.contentContainer}>
            <ScrollView
              style={styles.scrollContent}
              contentContainerStyle={styles.scrollContentContainer}
              showsVerticalScrollIndicator={true}
              nestedScrollEnabled={true}
              keyboardShouldPersistTaps="handled"
              scrollEventThrottle={16}
              bounces={Platform.OS === 'ios'}
              overScrollMode={Platform.OS === 'android' ? 'always' : 'auto'}
            >
            {/* Image */}
            <View style={styles.imageContainer}>
              {location.images && location.images.length > 0 && (
                <Image
                  source={{ uri: location.images[0] }}
                  style={styles.image}
                  resizeMode="cover"
                />
              )}
              <View style={[styles.categoryBadge, { backgroundColor: categoryInfo?.primary || COLORS.primary }]}>
                <Text style={styles.categoryIcon}>{categoryInfo?.icon || '📍'}</Text>
                <Text style={styles.categoryText}>{location.category.toUpperCase()}</Text>
              </View>
            </View>

            {/* Title and Location */}
            <View style={styles.titleSection}>
              <Text style={styles.title}>{location.name}</Text>
              <View style={styles.locationRow}>
                <Text style={styles.locationIcon}>📍</Text>
                <Text style={styles.locationText}>{location.location}</Text>
              </View>
            </View>

            {/* Quick Info */}
            <View style={styles.quickInfoContainer}>
              <View style={styles.quickInfoItem}>
                <Text style={styles.quickInfoIcon}>⏱️</Text>
                <Text style={styles.quickInfoLabel}>Duration</Text>
                <Text style={styles.quickInfoValue}>{location.duration}</Text>
              </View>
              
              <View style={styles.quickInfoItem}>
                <Text style={styles.quickInfoIcon}>📊</Text>
                <Text style={styles.quickInfoLabel}>Difficulty</Text>
                <Text style={[styles.quickInfoValue, { color: difficultyColor }]}>
                  {location.difficulty}
                </Text>
              </View>
              
              {location.elevation && (
                <View style={styles.quickInfoItem}>
                  <Text style={styles.quickInfoIcon}>⛰️</Text>
                  <Text style={styles.quickInfoLabel}>Elevation</Text>
                  <Text style={styles.quickInfoValue}>{location.elevation}</Text>
                </View>
              )}
            </View>

            {/* Description */}
            <View style={styles.descriptionContainer}>
              <Text style={styles.description}>{location.description}</Text>
            </View>

            {/* Rating */}
            {location.rating && (
              <View style={styles.ratingContainer}>
                <View style={styles.ratingRow}>
                  <Text style={styles.ratingIcon}>⭐</Text>
                  <Text style={styles.ratingValue}>{location.rating}</Text>
                  <Text style={styles.ratingCount}>({location.reviewCount} reviews)</Text>
                </View>
              </View>
            )}

            {/* Best Time to Visit */}
            <View style={styles.bestTimeContainer}>
              <Text style={styles.bestTimeIcon}>🗓️</Text>
              <View style={styles.bestTimeText}>
                <Text style={styles.bestTimeLabel}>Best Time to Visit</Text>
                <Text style={styles.bestTimeValue}>{location.bestTimeToVisit}</Text>
              </View>
            </View>
            </ScrollView>

            {/* Action Buttons - Fixed at bottom */}
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[styles.actionButton, styles.navigateButton]}
                onPress={handleNavigate}
              >
                <Text style={styles.navigateButtonText}>🧭 Navigate</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, styles.detailsButton]}
                onPress={handleViewDetails}
              >
                <Text style={styles.detailsButtonText}>View Details</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  overlayTouch: {
    flex: 1,
  },
  modalContainer: {
    backgroundColor: COLORS.background,
    borderTopLeftRadius: BORDER_RADIUS.xl,
    borderTopRightRadius: BORDER_RADIUS.xl,
    maxHeight: '85%',
    minHeight: '50%',
    ...SHADOWS.xl,
  },
  header: {
    alignItems: 'center',
    paddingTop: SPACING.md,
    paddingHorizontal: SPACING.lg,
    position: 'relative',
  },
  dragHandle: {
    width: 40,
    height: 4,
    backgroundColor: COLORS.textSecondary,
    borderRadius: 2,
    opacity: 0.3,
  },
  closeButton: {
    position: 'absolute',
    right: SPACING.lg,
    top: SPACING.md,
    width: 40, // Increased touch target
    height: 40, // Increased touch target
    borderRadius: 20,
    backgroundColor: COLORS.backgroundSecondary,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000, // Ensure it's above other elements
    elevation: 10, // Android elevation
    ...SHADOWS.medium, // Add shadow for better visibility
  },
  closeButtonText: {
    fontSize: 18, // Slightly larger for better visibility
    color: COLORS.text, // Use primary text color for better contrast
    fontWeight: '700',
    lineHeight: 18, // Ensure proper alignment
  },
  contentContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  scrollContent: {
    flex: 1,
    maxHeight: '100%',
  },
  scrollContentContainer: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.xl,
    flexGrow: 1,
  },
  imageContainer: {
    position: 'relative',
    marginTop: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  image: {
    width: '100%',
    height: 200,
    borderRadius: BORDER_RADIUS.lg,
  },
  categoryBadge: {
    position: 'absolute',
    top: SPACING.md,
    left: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    ...SHADOWS.medium,
  },
  categoryIcon: {
    fontSize: 16,
    marginRight: SPACING.xs,
  },
  categoryText: {
    color: COLORS.textInverse,
    fontSize: 12,
    fontWeight: '700',
  },
  titleSection: {
    marginBottom: SPACING.lg,
  },
  title: {
    fontSize: 24,
    fontWeight: '800',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationIcon: {
    fontSize: 16,
    marginRight: SPACING.sm,
  },
  locationText: {
    fontSize: 16,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  quickInfoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.lg,
    backgroundColor: COLORS.backgroundCard,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
  },
  quickInfoItem: {
    alignItems: 'center',
    flex: 1,
  },
  quickInfoIcon: {
    fontSize: 20,
    marginBottom: SPACING.xs,
  },
  quickInfoLabel: {
    fontSize: 12,
    color: COLORS.textSecondary,
    fontWeight: '500',
    marginBottom: SPACING.xs,
  },
  quickInfoValue: {
    fontSize: 14,
    color: COLORS.text,
    fontWeight: '700',
    textAlign: 'center',
  },
  descriptionContainer: {
    marginBottom: SPACING.lg,
  },
  description: {
    fontSize: 16,
    color: COLORS.text,
    lineHeight: 24,
    fontWeight: '400',
  },
  ratingContainer: {
    marginBottom: SPACING.lg,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingIcon: {
    fontSize: 18,
    marginRight: SPACING.sm,
  },
  ratingValue: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.text,
    marginRight: SPACING.sm,
  },
  ratingCount: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  bestTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.backgroundCard,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  bestTimeIcon: {
    fontSize: 24,
    marginRight: SPACING.lg,
  },
  bestTimeText: {
    flex: 1,
  },
  bestTimeLabel: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
    marginBottom: SPACING.xs,
  },
  bestTimeValue: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '700',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: SPACING.md,
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.lg,
    paddingBottom: SPACING.md,
    backgroundColor: COLORS.background,
    borderTopWidth: 1,
    borderTopColor: COLORS.backgroundSecondary,
  },
  actionButton: {
    flex: 1,
    paddingVertical: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    alignItems: 'center',
    ...SHADOWS.medium,
  },
  navigateButton: {
    backgroundColor: COLORS.primary,
  },
  navigateButtonText: {
    color: COLORS.textInverse,
    fontSize: 16,
    fontWeight: '700',
  },
  detailsButton: {
    backgroundColor: COLORS.backgroundCard,
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  detailsButtonText: {
    color: COLORS.primary,
    fontSize: 16,
    fontWeight: '700',
  },
});

export default LocationDetailsModal;
