import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { COLORS, SPACING, BORDER_RADIUS, createTextStyle } from '../utils/constants';

const ErrorState = ({
  title = 'Unable to load data',
  message = 'Please check your internet connection and try again.',
  onRetry,
  retryText = 'Try Again',
  isRetrying = false,
  icon = '📡',
  showRetry = true,
  style,
}) => {
  return (
    <View style={[styles.container, style]}>
      <Text style={styles.icon}>{icon}</Text>
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.message}>{message}</Text>
      
      {showRetry && onRetry && (
        <TouchableOpacity
          style={[styles.retryButton, isRetrying && styles.retryButtonDisabled]}
          onPress={onRetry}
          disabled={isRetrying}
          activeOpacity={0.8}
        >
          {isRetrying ? (
            <View style={styles.retryContent}>
              <ActivityIndicator size="small" color={COLORS.textInverse} />
              <Text style={styles.retryButtonText}>Retrying...</Text>
            </View>
          ) : (
            <Text style={styles.retryButtonText}>{retryText}</Text>
          )}
        </TouchableOpacity>
      )}
    </View>
  );
};

// Specific error state variants
export const NetworkErrorState = (props) => (
  <ErrorState
    title="Connection Problem"
    message="Unable to connect to the server. Please check your internet connection."
    icon="📡"
    {...props}
  />
);

export const DataErrorState = (props) => (
  <ErrorState
    title="Data Unavailable"
    message="We couldn't load the latest data. Please try refreshing."
    icon="📊"
    {...props}
  />
);

export const SearchErrorState = (props) => (
  <ErrorState
    title="Search Failed"
    message="Unable to perform search. Please try again."
    icon="🔍"
    {...props}
  />
);

export const LocationErrorState = (props) => (
  <ErrorState
    title="Location Unavailable"
    message="Unable to access your location. Please check your location settings."
    icon="📍"
    showRetry={false}
    {...props}
  />
);

export const ImageErrorState = (props) => (
  <ErrorState
    title="Image Failed to Load"
    message="The image couldn't be loaded."
    icon="🖼️"
    showRetry={false}
    style={styles.imageErrorContainer}
    {...props}
  />
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.xxl,
  },
  icon: {
    fontSize: 48,
    marginBottom: SPACING.lg,
    opacity: 0.7,
  },
  title: {
    ...createTextStyle(18, 'bold'),
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.md,
  },
  message: {
    ...createTextStyle(14, 'regular'),
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: SPACING.xl,
    maxWidth: 280,
  },
  retryButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    minWidth: 120,
  },
  retryButtonDisabled: {
    backgroundColor: COLORS.textSecondary,
    opacity: 0.7,
  },
  retryContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  retryButtonText: {
    ...createTextStyle(16, 'medium'),
    color: COLORS.textInverse,
    textAlign: 'center',
    marginLeft: SPACING.sm,
  },
  imageErrorContainer: {
    flex: 0,
    paddingVertical: SPACING.lg,
    minHeight: 120,
  },
});

export default ErrorState;
