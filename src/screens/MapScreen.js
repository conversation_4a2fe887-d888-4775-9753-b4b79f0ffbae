import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  Alert,
  Linking,
  Switch,
  ScrollView,
  StatusBar,
  Platform,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import MapboxMapView from '../components/MapboxMapView';
import LocationDetailsModal from '../components/LocationDetailsModal';

import LocalDataService from '../services/LocalDataService';
import { COLORS, SPACING, BORDER_RADIUS, SHADOWS } from '../utils/constants';
import { useToast } from '../contexts/ToastContext';
import ErrorState, { DataErrorState } from '../components/ErrorState';
// Map configuration
const MAP_CONFIG = {
  defaultRegion: {
    latitude: 18.5204,
    longitude: 73.8567,
    latitudeDelta: 2.0,
    longitudeDelta: 2.0,
  },
};

const MapScreen = ({ navigation }) => {
  const insets = useSafeAreaInsets();
  const [locations, setLocations] = useState([]);
  const [filteredLocations, setFilteredLocations] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const toast = useToast();

  // Load data from LocalDataService with lazy loading
  useEffect(() => {
    const loadDataWithLazyLoading = async () => {
      try {
        setIsLoading(true);

        // Phase 1: Load text data immediately
        const allData = await LocalDataService.getAllData();

        if (allData.length === 0) {
          console.warn('MapScreen: No data loaded from LocalDataService. This might be due to Backblaze B2 connection issues.');
        }

        // Set data immediately for UI rendering
        setLocations(allData);
        setFilteredLocations(allData);
        setIsLoading(false); // Allow UI to render immediately

      } catch (error) {
        // Set empty arrays as fallback when Backblaze B2 data loading fails
        setLocations([]);
        setFilteredLocations([]);
        setError(error.message);
        setIsLoading(false);
      }
    };

    loadDataWithLazyLoading();
  }, []);

  // Pull-to-refresh handler with lazy loading
  const onRefresh = useCallback(async () => {
    try {
      setRefreshing(true);

      // Clear RemoteDataService cache
      const RemoteDataService = require('../services/RemoteDataService').default;
      await RemoteDataService.clearCache();

      // Force refresh all data
      await LocalDataService.forceRefreshData();

      // Reload data
      const allData = await LocalDataService.getAllData();
      setLocations(allData);
      setFilteredLocations(allData);



      setError(null);
      toast.showRefreshSuccess();

    } catch (err) {
      setError(err.message);
      toast.showDataError(() => onRefresh());
    } finally {
      setRefreshing(false);
    }
  }, []);



  // Filter locations based on search and category
  useEffect(() => {
    // Ensure locations is an array before filtering
    if (!Array.isArray(locations)) {
      setFilteredLocations([]);
      return;
    }

    let filtered = [...locations]; // Create a copy to avoid mutations

    // Filter by category - use mapCategory which is normalized by LocalDataService
    if (activeFilter !== 'all') {
      filtered = filtered.filter(location => {
        // LocalDataService sets mapCategory for consistent filtering
        const categoryToMatch = location.mapCategory || location.category;
        return categoryToMatch === activeFilter;
      });
    }

    // Filter by search text
    if (searchText.trim()) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(location => {
        const nameMatch = location.name && location.name.toLowerCase().includes(searchLower);
        const locationMatch = location.location && location.location.toLowerCase().includes(searchLower);
        const categoryMatch = location.category && location.category.toLowerCase().includes(searchLower);
        return nameMatch || locationMatch || categoryMatch;
      });
    }

    setFilteredLocations(filtered);
  }, [searchText, activeFilter, locations]);

  const handleLocationPress = (location) => {
    setSelectedLocation(location);
    setModalVisible(true);
  };

  // Calculate bounds for filtered locations
  const calculateBounds = (locations) => {
    if (!locations || locations.length === 0) {
      return null;
    }

    const validLocations = locations.filter(loc =>
      loc && loc.coordinates &&
      typeof loc.coordinates.latitude === 'number' &&
      typeof loc.coordinates.longitude === 'number'
    );

    if (validLocations.length === 0) {
      return null;
    }

    if (validLocations.length === 1) {
      // Single location - return center with some padding
      const loc = validLocations[0];
      return {
        center: [loc.coordinates.longitude, loc.coordinates.latitude],
        zoom: 12
      };
    }

    // Multiple locations - calculate bounds
    let minLat = validLocations[0].coordinates.latitude;
    let maxLat = validLocations[0].coordinates.latitude;
    let minLng = validLocations[0].coordinates.longitude;
    let maxLng = validLocations[0].coordinates.longitude;

    validLocations.forEach(location => {
      const { latitude, longitude } = location.coordinates;
      minLat = Math.min(minLat, latitude);
      maxLat = Math.max(maxLat, latitude);
      minLng = Math.min(minLng, longitude);
      maxLng = Math.max(maxLng, longitude);
    });

    // Add padding to bounds
    const latPadding = (maxLat - minLat) * 0.1;
    const lngPadding = (maxLng - minLng) * 0.1;

    return {
      bounds: [
        [minLng - lngPadding, minLat - latPadding], // Southwest
        [maxLng + lngPadding, maxLat + latPadding]  // Northeast
      ]
    };
  };

  const handleNavigate = (location) => {
    const { latitude, longitude } = location.coordinates;
    const url = `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`;

    Linking.canOpenURL(url)
      .then(supported => {
        if (supported) {
          Linking.openURL(url);
        } else {
          Alert.alert('Error', 'Unable to open navigation app');
        }
      })
      .catch(err => {
        Alert.alert('Error', 'Unable to open navigation app');
      });
  };

  const handleViewDetails = (location) => {
    navigation.navigate('TrekDetails', { trek: location });
  };



  const getFilterCount = (filter) => {
    if (!Array.isArray(locations)) return 0;

    if (filter === 'all') {
      return locations.length;
    }

    const count = locations.filter(location => {
      const categoryToMatch = location.mapCategory || location.category;
      const matches = categoryToMatch === filter;
      return matches;
    }).length;


    return count;
  };

  const renderFilterButton = (filter, label, icon) => {
    const count = getFilterCount(filter);
    return (
      <TouchableOpacity
        key={filter}
        style={[
          styles.filterButton,
          activeFilter === filter && styles.activeFilterButton,
        ]}
        onPress={() => setActiveFilter(filter)}
      >
        <Text style={[
          styles.filterIcon,
          activeFilter === filter && styles.activeFilterIcon,
        ]}>
          {icon}
        </Text>
        <Text style={[
          styles.filterText,
          activeFilter === filter && styles.activeFilterText,
        ]}>
          {label}
        </Text>
        {count > 0 && (
          <View style={[
            styles.filterCount,
            activeFilter === filter && styles.activeFilterCount,
          ]}>
            <Text style={[
              styles.filterCountText,
              activeFilter === filter && styles.activeFilterCountText,
            ]}>
              {count}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.background} translucent={false} />
      {/* Header */}
      <View style={[styles.header, { paddingTop: Math.max(insets.top, SPACING.md) }]}>
        <Text style={styles.headerTitle}>Paiwat</Text>

      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Text style={styles.searchIcon}>🔍</Text>
          <TextInput
            style={styles.searchInput}
            placeholder="Search locations..."
            placeholderTextColor={COLORS.textSecondary}
            value={searchText}
            onChangeText={setSearchText}
            returnKeyType="search"
            blurOnSubmit={false}
            autoCorrect={false}
            autoCapitalize="none"
            keyboardType="default"
            clearButtonMode="while-editing"
          />
          {searchText.length > 0 && (
            <TouchableOpacity onPress={() => setSearchText('')}>
              <Text style={styles.clearIcon}>✕</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Filter Buttons */}
      <View style={styles.filtersContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filtersScrollContent}
        >
          {renderFilterButton('all', 'All', '🗺️')}
          {renderFilterButton('fort', 'Forts', '🏰')}
          {renderFilterButton('waterfall', 'Waterfalls', '💧')}
          {renderFilterButton('trek', 'Treks', '🥾')}
          {renderFilterButton('cave', 'Caves', '🕳️')}
        </ScrollView>
      </View>

      {/* Map with Pull-to-Refresh */}
      <ScrollView
        style={styles.mapScrollContainer}
        contentContainerStyle={styles.mapScrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[COLORS.primary]}
            tintColor={COLORS.primary}
            title="Pull to refresh locations..."
            titleColor={COLORS.textSecondary}
          />
        }
        scrollEnabled={false} // Disable scrolling to prevent conflicts with map
      >
        <View style={styles.mapContainer}>
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={COLORS.primary} />
              <Text style={styles.loadingText}>Loading locations...</Text>
            </View>
          ) : error ? (
            <DataErrorState
              title="Unable to Load Locations"
              message={error}
              onRetry={() => {
                setError(null);
                setIsLoading(true);
                // Trigger data reload
                const loadDataWithLazyLoading = async () => {
                  try {
                    const allData = await LocalDataService.getAllData();
                    setLocations(allData);
                    setFilteredLocations(allData);
                  } catch (err) {
                    setError(err.message);
                  } finally {
                    setIsLoading(false);
                  }
                };
                loadDataWithLazyLoading();
              }}
              isRetrying={isLoading}
            />
          ) : (
            <MapboxMapView
              key={`map-${activeFilter}-${filteredLocations.length}`}
              locations={filteredLocations}
              selectedLocation={selectedLocation}
              onLocationPress={handleLocationPress}
              showUserLocation={true}
              initialCenter={MAP_CONFIG.defaultRegion}
              bounds={calculateBounds(filteredLocations)}
              activeFilter={activeFilter}
            />
          )}
        </View>

        {/* Results Count */}
        <View style={styles.resultsContainer}>
          <Text style={styles.resultsText}>
            {isLoading
              ? 'Loading locations...'
              : `${filteredLocations.length} location${filteredLocations.length !== 1 ? 's' : ''} found`
            }
          </Text>
        </View>
      </ScrollView>

      {/* Location Details Modal */}
      <LocationDetailsModal
        visible={modalVisible}
        location={selectedLocation}
        onClose={() => setModalVisible(false)}
        onNavigate={handleNavigate}
        onViewDetails={handleViewDetails}
      />


    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.md,
    backgroundColor: COLORS.background,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.backgroundSecondary,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: COLORS.text,
  },

  mapToggleButton: {
    backgroundColor: COLORS.backgroundCard,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: COLORS.success,
    ...SHADOWS.small,
  },
  mapToggleText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.success,
  },
  styleButton: {
    backgroundColor: COLORS.backgroundCard,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    ...SHADOWS.small,
  },
  styleButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.primary,
  },
  searchContainer: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.backgroundCard,
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    ...SHADOWS.small,
  },
  searchIcon: {
    fontSize: 18,
    marginRight: SPACING.md,
    color: COLORS.textSecondary,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '500',
  },
  clearIcon: {
    fontSize: 16,
    color: COLORS.textSecondary,
    fontWeight: '600',
  },
  filtersContainer: {
    paddingBottom: SPACING.md,
  },
  filtersScrollContent: {
    paddingHorizontal: SPACING.lg,
    gap: SPACING.sm,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.backgroundCard,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 1,
    borderColor: COLORS.backgroundSecondary,
    marginRight: SPACING.sm,
  },
  activeFilterButton: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  filterIcon: {
    fontSize: 16,
    marginRight: SPACING.xs,
  },
  filterText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.text,
  },
  activeFilterIcon: {
    color: COLORS.textInverse,
  },
  activeFilterText: {
    color: COLORS.textInverse,
  },
  filterCount: {
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: SPACING.xs,
  },
  activeFilterCount: {
    backgroundColor: COLORS.textInverse,
  },
  filterCountText: {
    fontSize: 12,
    fontWeight: '700',
    color: COLORS.textInverse,
  },
  activeFilterCountText: {
    color: COLORS.primary,
  },
  mapScrollContainer: {
    flex: 1,
  },
  mapScrollContent: {
    flex: 1,
  },
  mapContainer: {
    flex: 1,
    margin: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
    ...SHADOWS.medium,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.backgroundCard,
  },
  loadingText: {
    marginTop: SPACING.md,
    fontSize: 16,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  resultsContainer: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.backgroundCard,
    borderTopWidth: 1,
    borderTopColor: COLORS.backgroundSecondary,
  },
  resultsText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default MapScreen;
