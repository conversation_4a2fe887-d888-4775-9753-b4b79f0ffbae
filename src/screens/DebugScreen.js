/**
 * Debug Screen - For viewing device information and logs
 * Only available in development builds
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Share,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import deviceLogger from '../utils/DeviceLogger';
import { COLORS, FONTS, SPACING } from '../utils/constants';

const DebugScreen = ({ navigation }) => {
  const [logs, setLogs] = useState([]);
  const [deviceInfo, setDeviceInfo] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState('ALL');

  useEffect(() => {
    loadDebugData();
  }, []);

  const loadDebugData = async () => {
    try {
      const recentLogs = deviceLogger.getRecentLogs(100);
      const deviceSummary = deviceLogger.getDeviceSummary();
      
      setLogs(recentLogs);
      setDeviceInfo(deviceSummary);
    } catch (error) {
      console.error('Failed to load debug data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDebugData();
    setRefreshing(false);
  };

  const exportLogs = async () => {
    try {
      const exportData = await deviceLogger.exportLogs();
      
      await Share.share({
        message: exportData,
        title: 'Debug Logs Export',
      });
    } catch (error) {
      Alert.alert('Export Failed', error.message);
    }
  };

  const clearLogs = () => {
    Alert.alert(
      'Clear Logs',
      'Are you sure you want to clear all debug logs?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            await deviceLogger.clearLogs();
            await loadDebugData();
          },
        },
      ]
    );
  };

  const getFilteredLogs = () => {
    if (selectedLevel === 'ALL') return logs;
    return logs.filter(log => log.level === selectedLevel);
  };

  const getLevelColor = (level) => {
    switch (level) {
      case 'ERROR': return '#FF6B6B';
      case 'WARN': return '#FFB347';
      case 'INFO': return '#4ECDC4';
      default: return COLORS.text;
    }
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const filteredLogs = getFilteredLogs();

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Debug Console</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity onPress={exportLogs} style={styles.actionButton}>
            <Text style={styles.actionButtonText}>Export</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={clearLogs} style={styles.actionButton}>
            <Text style={styles.actionButtonText}>Clear</Text>
          </TouchableOpacity>
        </View>
      </View>

      {deviceInfo && (
        <View style={styles.deviceInfo}>
          <Text style={styles.deviceInfoTitle}>Device Information</Text>
          <Text style={styles.deviceInfoText}>{deviceInfo}</Text>
        </View>
      )}

      <View style={styles.filterContainer}>
        {['ALL', 'ERROR', 'WARN', 'INFO'].map(level => (
          <TouchableOpacity
            key={level}
            onPress={() => setSelectedLevel(level)}
            style={[
              styles.filterButton,
              selectedLevel === level && styles.filterButtonActive
            ]}
          >
            <Text style={[
              styles.filterButtonText,
              selectedLevel === level && styles.filterButtonTextActive
            ]}>
              {level}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView
        style={styles.logsContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {filteredLogs.length === 0 ? (
          <Text style={styles.noLogsText}>No logs available</Text>
        ) : (
          filteredLogs.reverse().map((log, index) => (
            <View key={index} style={styles.logEntry}>
              <View style={styles.logHeader}>
                <Text style={[styles.logLevel, { color: getLevelColor(log.level) }]}>
                  {log.level}
                </Text>
                <Text style={styles.logTimestamp}>
                  {formatTimestamp(log.timestamp)}
                </Text>
              </View>
              <Text style={styles.logMessage}>{log.message}</Text>
              {log.context && (
                <Text style={styles.logContext}>
                  {typeof log.context === 'string' 
                    ? log.context 
                    : JSON.stringify(log.context, null, 2)
                  }
                </Text>
              )}
            </View>
          ))
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  backButton: {
    fontSize: 16,
    color: COLORS.primary,
    fontFamily: FONTS.medium,
  },
  title: {
    fontSize: 18,
    fontFamily: FONTS.bold,
    color: COLORS.text,
  },
  headerActions: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  actionButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    backgroundColor: COLORS.primary,
    borderRadius: 6,
  },
  actionButtonText: {
    color: COLORS.white,
    fontSize: 12,
    fontFamily: FONTS.medium,
  },
  deviceInfo: {
    padding: SPACING.lg,
    backgroundColor: COLORS.surface,
    margin: SPACING.lg,
    borderRadius: 8,
  },
  deviceInfoTitle: {
    fontSize: 14,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  deviceInfoText: {
    fontSize: 12,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
    gap: SPACING.sm,
  },
  filterButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 6,
    backgroundColor: COLORS.surface,
  },
  filterButtonActive: {
    backgroundColor: COLORS.primary,
  },
  filterButtonText: {
    fontSize: 12,
    fontFamily: FONTS.medium,
    color: COLORS.textSecondary,
  },
  filterButtonTextActive: {
    color: COLORS.white,
  },
  logsContainer: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  noLogsText: {
    textAlign: 'center',
    fontSize: 14,
    color: COLORS.textSecondary,
    fontFamily: FONTS.regular,
    marginTop: SPACING.xl,
  },
  logEntry: {
    backgroundColor: COLORS.surface,
    padding: SPACING.md,
    marginBottom: SPACING.sm,
    borderRadius: 6,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.border,
  },
  logHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  logLevel: {
    fontSize: 12,
    fontFamily: FONTS.bold,
  },
  logTimestamp: {
    fontSize: 10,
    color: COLORS.textSecondary,
    fontFamily: FONTS.regular,
  },
  logMessage: {
    fontSize: 13,
    color: COLORS.text,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.xs,
  },
  logContext: {
    fontSize: 11,
    color: COLORS.textSecondary,
    fontFamily: FONTS.regular,
    backgroundColor: COLORS.background,
    padding: SPACING.sm,
    borderRadius: 4,
  },
});

export default DebugScreen;
