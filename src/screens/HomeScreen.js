import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  StatusBar,
  FlatList,
  Image,
  TextInput,
  Platform,
  ActivityIndicator,
  Animated,
  Keyboard,
  Alert,
  RefreshControl,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { COLORS, CATEGORIES, CATEGORY_COLORS, SHADOWS, SPACING, BORDER_RADIUS, FONTS, TYPOGRAPHY, NAVIGATION, createTextStyle } from '../utils/constants';
import { getImageSource } from '../utils/imageUtils';
import LocalDataService from '../services/LocalDataService';
import NearbyTreks from '../components/NearbyTreks';
import ErrorBoundary from '../components/ErrorBoundary';
import { useErrorHandler } from '../hooks/useErrorHandler';
import { useToast } from '../contexts/ToastContext';
import ErrorState, { DataErrorState } from '../components/ErrorState';

const { width, height } = Dimensions.get('window');

const HomeScreen = ({ navigation }) => {
  const [searchText, setSearchText] = useState('');
  const insets = useSafeAreaInsets();
  const { handleError } = useErrorHandler();
  const toast = useToast();

  // State for async data loading
  const [allData, setAllData] = useState([]);
  const [topTreks, setTopTreks] = useState([]);
  const [waterfallTreks, setWaterfallTreks] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Pull-to-refresh state
  const [refreshing, setRefreshing] = useState(false);

  // Lazy loading states
  const [dataLoaded, setDataLoaded] = useState(false);
  const [imagesLoading, setImagesLoading] = useState(true);

  // Search state
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const searchInputRef = useRef(null);
  const searchOverlayOpacity = useRef(new Animated.Value(0)).current;
  const debounceTimeout = useRef(null);

  // Dynamic greeting based on time of day
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) {
      return 'Good morning 👋';
    } else if (hour < 17) {
      return 'Good afternoon ☀️';
    } else if (hour < 21) {
      return 'Good evening 🌅';
    } else {
      return 'Good night 🌙';
    }
  };

  // Load data with lazy loading approach
  useEffect(() => {
    const loadDataWithLazyLoading = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Phase 1: Load text data immediately (fast)
        const [allDataResult, topTreksResult] = await Promise.all([
          LocalDataService.getAllData(),
          LocalDataService.getFeaturedData(5)
        ]);

        // Get waterfall data with fallback logic
        const topRatedWaterfalls = await LocalDataService.getTopRatedByCategory('waterfall', 5);
        const waterfallResult = topRatedWaterfalls.length > 0 ?
          topRatedWaterfalls :
          (await LocalDataService.getDataByCategory('waterfall')).slice(0, 5);

        // Set data immediately - UI can render with text content
        setAllData(allDataResult);
        setTopTreks(topTreksResult);
        setWaterfallTreks(waterfallResult);
        setDataLoaded(true);
        setIsLoading(false); // Allow UI to render immediately

        // Phase 2: Load images in background (slower, non-blocking)
        setTimeout(() => {
          preloadImages([...topTreksResult, ...waterfallResult]);
        }, 100); // Small delay to ensure UI renders first

      } catch (err) {
        setError(err.message);
        handleError(err);
        toast.showDataError(() => loadDataWithLazyLoading());

        // Set empty arrays as fallback
        setAllData([]);
        setTopTreks([]);
        setWaterfallTreks([]);
        setIsLoading(false);
      }
    };

    loadDataWithLazyLoading();
  }, [handleError]);

  // Preload images in background without blocking UI
  const preloadImages = async (items) => {
    try {
      setImagesLoading(true);

      // Preload images for visible items
      const imagePromises = items.slice(0, 6).map(async (item) => {
        try {
          const imageSource = getTrekImageSource(item);
          if (imageSource && typeof imageSource === 'object' && imageSource.uri) {
            // Preload network images
            await Image.prefetch(imageSource.uri);
          }
          return true;
        } catch (error) {
          // Silently handle image preload failures
          return false;
        }
      });

      await Promise.allSettled(imagePromises);

    } catch (error) {
      // Silently handle image preloading failures
    } finally {
      setImagesLoading(false);
    }
  };

  // Cleanup debounce timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }
    };
  }, []);

  // Search functionality
  const performSearch = useCallback((query) => {
    if (!query.trim()) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);
    const lowercaseQuery = query.toLowerCase();

    const results = allData.filter(item => {
      // Search in name
      if (item.name && item.name.toLowerCase().includes(lowercaseQuery)) {
        return true;
      }

      // Search in location/district
      if (item.location && item.location.toLowerCase().includes(lowercaseQuery)) {
        return true;
      }

      // Search in description
      if (item.description && item.description.toLowerCase().includes(lowercaseQuery)) {
        return true;
      }

      // Search in district
      if (item.district && item.district.toLowerCase().includes(lowercaseQuery)) {
        return true;
      }

      // Search in category
      if (item.category && item.category.toLowerCase().includes(lowercaseQuery)) {
        return true;
      }

      return false;
    });

    setSearchResults(results);
    setIsSearching(false);
  }, [allData]);

  const debouncedSearch = useCallback((query) => {
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }

    debounceTimeout.current = setTimeout(() => {
      performSearch(query);
    }, 300);
  }, [performSearch]);

  const handleSearchTextChange = (text) => {
    setSearchText(text);
    debouncedSearch(text);
  };

  const openSearch = () => {
    setIsSearchActive(true);
    Animated.timing(searchOverlayOpacity, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      searchInputRef.current?.focus();
    });
  };

  const closeSearch = () => {
    Keyboard.dismiss();
    setSearchText('');
    setSearchResults([]);
    setIsSearching(false);

    Animated.timing(searchOverlayOpacity, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      setIsSearchActive(false);
    });
  };

  const handleSearchResultPress = (item) => {
    closeSearch();
    navigation.navigate('TrekDetails', { trek: item });
  };

  const handleTrekPress = (trek) => {
    navigation.navigate('TrekDetails', { trek });
  };

  const handleViewAllPress = () => {
    navigation.navigate('TrekList', { category: null });
  };

  const handleViewAll = () => {
    navigation.navigate('TrekList', { category: null });
  };

  // Pull-to-refresh handler with lazy loading
  const onRefresh = useCallback(async () => {
    try {
      setRefreshing(true);
      setError(null);

      // Clear RemoteDataService cache
      const RemoteDataService = require('../services/RemoteDataService').default;
      await RemoteDataService.clearCache();

      // Force refresh all data
      await LocalDataService.forceRefreshData();

      // Phase 1: Load text data immediately
      const [allDataResult, topTreksResult] = await Promise.all([
        LocalDataService.getAllData(),
        LocalDataService.getFeaturedData(5)
      ]);

      // Get waterfall data with fallback logic
      const topRatedWaterfalls = await LocalDataService.getTopRatedByCategory('waterfall', 5);
      const waterfallResult = topRatedWaterfalls.length > 0 ?
        topRatedWaterfalls :
        (await LocalDataService.getDataByCategory('waterfall')).slice(0, 5);

      // Set data immediately
      setAllData(allDataResult);
      setTopTreks(topTreksResult);
      setWaterfallTreks(waterfallResult);
      setDataLoaded(true);

      // Show success message
      toast.showRefreshSuccess();

      // Phase 2: Preload images in background
      setTimeout(() => {
        preloadImages([...topTreksResult, ...waterfallResult]);
      }, 100);

    } catch (err) {
      setError(err.message);
      toast.showDataError(() => onRefresh());
    } finally {
      setRefreshing(false);
    }
  }, [toast]);









  const getTrekImageSource = (trek) => {
    return getImageSource(trek);
  };

  // Lazy Image Component with placeholder
  const LazyImage = ({ source, style, trek }) => {
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageError, setImageError] = useState(false);

    return (
      <View style={style}>
        {/* Placeholder while loading */}
        {!imageLoaded && !imageError && (
          <View style={[style, styles.imagePlaceholder]}>
            <View style={styles.placeholderContent}>
              <Text style={styles.placeholderIcon}>🏔️</Text>
              <Text style={styles.placeholderText}>{trek?.name?.charAt(0) || '?'}</Text>
            </View>
          </View>
        )}

        {/* Error placeholder */}
        {imageError && (
          <View style={[style, styles.imagePlaceholder]}>
            <View style={styles.placeholderContent}>
              <Text style={styles.placeholderIcon}>📷</Text>
              <Text style={styles.placeholderText}>No Image</Text>
            </View>
          </View>
        )}

        {/* Actual image */}
        {source && !imageError && (
          <Image
            source={source}
            style={[style, { opacity: imageLoaded ? 1 : 0 }]}
            onLoad={() => setImageLoaded(true)}
            onError={() => setImageError(true)}
            resizeMode="cover"
          />
        )}
      </View>
    );
  };

  // Search result item component
  const renderSearchResult = ({ item }) => {
    const imageSource = getTrekImageSource(item);

    return (
      <TouchableOpacity
        style={styles.searchResultItem}
        onPress={() => handleSearchResultPress(item)}
        activeOpacity={0.7}
      >
        <View style={styles.searchResultImageContainer}>
          {imageSource ? (
            <Image
              source={imageSource}
              style={styles.searchResultImage}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.searchResultImagePlaceholder}>
              <Text style={styles.searchResultImagePlaceholderText}>
                {item.category === 'fort' ? '🏰' :
                 item.category === 'waterfall' ? '💧' :
                 item.category === 'trek' ? '🏔️' : '🕳️'}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.searchResultContent}>
          <Text style={styles.searchResultName} numberOfLines={1}>
            {item.name}
          </Text>

          <Text style={styles.searchResultLocation} numberOfLines={1}>
            {item.location || item.district || 'Location not specified'}
          </Text>

          <View style={styles.searchResultMeta}>
            <View style={[styles.searchResultCategory, { backgroundColor: CATEGORY_COLORS[item.category] || COLORS.primary }]}>
              <Text style={styles.searchResultCategoryText}>
                {item.category ? item.category.charAt(0).toUpperCase() + item.category.slice(1) : 'Unknown'}
              </Text>
            </View>

            {item.difficulty && (
              <View style={styles.searchResultDifficulty}>
                <Text style={styles.searchResultDifficultyText}>
                  {item.difficulty}
                </Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return COLORS.success;
      case 'moderate': return COLORS.warning;
      case 'difficult': return COLORS.error;
      default: return COLORS.textSecondary;
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <ErrorBoundary title="Home Screen Error" message="Unable to load the home screen. Please try again.">
        <SafeAreaView style={styles.container}>
          <StatusBar barStyle="dark-content" backgroundColor={COLORS.background} translucent={false} />
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text style={styles.loadingText}>Loading trek data...</Text>
          </View>
        </SafeAreaView>
      </ErrorBoundary>
    );
  }

  // Show error state when there's an error (not loading)
  if (error) {
    return (
      <ErrorBoundary title="Home Screen Error" message="Unable to load the home screen. Please try again.">
        <SafeAreaView style={styles.container}>
          <StatusBar barStyle="dark-content" backgroundColor={COLORS.background} translucent={false} />
          <DataErrorState
            title="Unable to Load Data"
            message={error}
            onRetry={() => {
              setError(null);
              loadDataWithLazyLoading();
            }}
            isRetrying={isLoading}
          />
        </SafeAreaView>
      </ErrorBoundary>
    );
  }



  return (
    <ErrorBoundary title="Home Screen Error" message="Unable to load the home screen. Please try again.">
      <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.background} translucent={false} />

      {/* Clean Modern Header */}
      <View style={[styles.header, { paddingTop: Math.max(insets.top, SPACING.lg) }]}>
        <View style={styles.headerTop}>
          <View>
            <Text style={styles.greeting}>{getGreeting()}</Text>
          </View>
          <TouchableOpacity style={styles.profileButton}>
            <View style={styles.profileImage} />
          </TouchableOpacity>
        </View>

        {/* Clean Search Bar */}
        <TouchableOpacity style={styles.searchContainer} onPress={openSearch} activeOpacity={0.7}>
          <View style={styles.searchBar}>
            <Text style={styles.searchIcon}>🔍</Text>
            <Text style={styles.searchPlaceholder}>Search treks, forts, waterfalls...</Text>
          </View>
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="on-drag"
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[COLORS.primary]}
            tintColor={COLORS.primary}
            title="Pull to refresh data..."
            titleColor={COLORS.textSecondary}
          />
        }
      >

        {/* Categories - Clean Icons */}
        <View style={styles.categoriesSection}>
          <View style={styles.categoriesGrid}>
            {[
              { id: 'fort', title: 'Forts', icon: '🏰', color: COLORS.fort },
              { id: 'waterfall', title: 'Waterfalls', icon: '💧', color: COLORS.waterfall },
              { id: 'trek', title: 'Treks', icon: '🥾', color: COLORS.trek },
              { id: 'cave', title: 'Caves', icon: '🕳️', color: COLORS.cave },
            ].map((category, index) => (
              <TouchableOpacity
                key={category.id}
                style={styles.categoryItem}
                onPress={() => navigation.navigate('TrekList', { category: category.id })}
                activeOpacity={0.7}
              >
                <View style={[styles.categoryIconContainer, { backgroundColor: category.color + '15' }]}>
                  <Text style={styles.categoryIcon}>{category.icon}</Text>
                </View>
                <Text style={styles.categoryLabel}>{category.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Nearby Treks - Exclude featured destinations */}
        <NearbyTreks
          treks={allData}
          navigation={navigation}
          maxDistance={100}
          limit={6}
          excludeTreks={topTreks} // Exclude featured destinations
        />

        {/* Featured Destinations - Clean Cards */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Destinations</Text>
            <TouchableOpacity onPress={handleViewAllPress}>
              <Text style={styles.viewAllText}>View all</Text>
            </TouchableOpacity>
          </View>

          <FlatList
            data={topTreks}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.featuredCard}
                onPress={() => handleTrekPress(item)}
                activeOpacity={0.8}
              >
                <LazyImage
                  source={getTrekImageSource(item)}
                  style={styles.featuredImage}
                  trek={item}
                />
                <View style={styles.featuredContent}>
                  <View style={styles.featuredHeader}>
                    <Text
                      style={styles.featuredTitle}
                      numberOfLines={2}
                      ellipsizeMode="tail"
                    >
                      {item.name}
                    </Text>
                    <View style={styles.ratingContainer}>
                      <Text style={styles.ratingText}>⭐ {item.rating}</Text>
                    </View>
                  </View>
                  <Text
                    style={styles.featuredLocation}
                    numberOfLines={2}
                    ellipsizeMode="tail"
                  >
                    {item.location}
                  </Text>
                  <View style={styles.featuredFooter}>
                    <Text
                      style={styles.featuredDifficulty}
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {item.difficulty}
                    </Text>
                    <Text
                      style={styles.featuredDuration}
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {item.duration}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            )}
            keyExtractor={(item, index) => `top-trek-${item.id}-${index}`}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.featuredList}
            decelerationRate="fast"
            snapToInterval={(Platform.OS === 'android' ? Math.min(width * 0.68, 270) : Math.min(width * 0.7, 280)) + SPACING.lg}
            snapToAlignment="start"
          />
        </View>

        {/* Popular Waterfalls - Clean Cards */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>🏞️ Top Rated Waterfalls</Text>
            <TouchableOpacity onPress={() => navigation.navigate('TrekList', { category: 'waterfall' })}>
              <Text style={styles.viewAllText}>View all</Text>
            </TouchableOpacity>
          </View>

          <FlatList
            data={waterfallTreks}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.featuredCard}
                onPress={() => handleTrekPress(item)}
                activeOpacity={0.8}
              >
                <LazyImage
                  source={getTrekImageSource(item)}
                  style={styles.featuredImage}
                  trek={item}
                />
                <View style={styles.featuredContent}>
                  <View style={styles.featuredHeader}>
                    <Text
                      style={styles.featuredTitle}
                      numberOfLines={2}
                      ellipsizeMode="tail"
                    >
                      {item.name}
                    </Text>
                    <View style={styles.ratingContainer}>
                      <Text style={styles.ratingText}>⭐ {item.rating}</Text>
                    </View>
                  </View>
                  <Text
                    style={styles.featuredLocation}
                    numberOfLines={2}
                    ellipsizeMode="tail"
                  >
                    {item.location}
                  </Text>
                  <View style={styles.featuredFooter}>
                    <Text
                      style={styles.featuredDifficulty}
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {item.difficulty}
                    </Text>
                    <Text
                      style={styles.featuredDuration}
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {item.duration}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            )}
            keyExtractor={(item, index) => `waterfall-${item.id}-${index}`}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.featuredList}
            decelerationRate="fast"
            snapToInterval={(Platform.OS === 'android' ? Math.min(width * 0.68, 270) : Math.min(width * 0.7, 280)) + SPACING.lg}
            snapToAlignment="start"
          />
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={() => navigation.navigate('TrekPlanner')}
              activeOpacity={0.8}
            >
              <View style={styles.quickActionIcon}>
                <Text style={styles.quickActionEmoji}>🧭</Text>
              </View>
              <Text style={styles.quickActionTitle}>Plan Trek</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={() => navigation.navigate('Map')}
              activeOpacity={0.8}
            >
              <View style={styles.quickActionIcon}>
                <Text style={styles.quickActionEmoji}>🗺️</Text>
              </View>
              <Text style={styles.quickActionTitle}>Explore Map</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={() => navigation.navigate('Emergency')}
              activeOpacity={0.8}
            >
              <View style={styles.quickActionIcon}>
                <Text style={styles.quickActionEmoji}>🚨</Text>
              </View>
              <Text style={styles.quickActionTitle}>Emergency</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={() => navigation.navigate('My Treks')}
              activeOpacity={0.8}
            >
              <View style={styles.quickActionIcon}>
                <Text style={styles.quickActionEmoji}>📋</Text>
              </View>
              <Text style={styles.quickActionTitle}>My Treks</Text>
            </TouchableOpacity>
          </View>
        </View>



        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Search Overlay */}
      {isSearchActive && (
        <Animated.View
          style={[
            styles.searchOverlay,
            {
              opacity: searchOverlayOpacity,
              paddingTop: Math.max(insets.top, SPACING.lg),
            }
          ]}
        >
          {/* Search Header */}
          <View style={styles.searchHeader}>
            <View style={styles.searchInputContainer}>
              <Text style={styles.searchInputIcon}>🔍</Text>
              <TextInput
                ref={searchInputRef}
                style={styles.searchInputField}
                placeholder="Search treks, forts, waterfalls..."
                placeholderTextColor={COLORS.textSecondary}
                value={searchText}
                onChangeText={handleSearchTextChange}
                returnKeyType="search"
                autoCorrect={false}
                autoCapitalize="none"
                clearButtonMode="while-editing"
              />
            </View>
            <TouchableOpacity
              style={styles.searchCancelButton}
              onPress={closeSearch}
              activeOpacity={0.7}
            >
              <Text style={styles.searchCancelText}>Cancel</Text>
            </TouchableOpacity>
          </View>

          {/* Search Results */}
          <View style={styles.searchContent}>
            {isSearching ? (
              <View style={styles.searchLoadingContainer}>
                <ActivityIndicator size="small" color={COLORS.primary} />
                <Text style={styles.searchLoadingText}>Searching...</Text>
              </View>
            ) : searchText.trim() === '' ? (
              <View style={styles.searchEmptyContainer}>
                <Text style={styles.searchEmptyIcon}>🔍</Text>
                <Text style={styles.searchEmptyTitle}>Search Destinations</Text>
                <Text style={styles.searchEmptyText}>
                  Find your next adventure among forts, waterfalls, treks, and caves
                </Text>
              </View>
            ) : searchResults.length === 0 ? (
              <View style={styles.searchEmptyContainer}>
                <Text style={styles.searchEmptyIcon}>😔</Text>
                <Text style={styles.searchEmptyTitle}>No Results Found</Text>
                <Text style={styles.searchEmptyText}>
                  Try searching with different keywords
                </Text>
              </View>
            ) : (
              <FlatList
                data={searchResults}
                renderItem={renderSearchResult}
                keyExtractor={(item, index) => `search-${item.id}-${index}`}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.searchResultsList}
              />
            )}
          </View>
        </Animated.View>
      )}
    </SafeAreaView>
    </ErrorBoundary>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },

  // Clean Header Styles
  header: {
    backgroundColor: COLORS.background,
    paddingHorizontal: SPACING.xl,
    paddingBottom: SPACING.lg,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.lg,
  },
  greeting: {
    ...createTextStyle(14, 'regular'),
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  appName: {
    ...createTextStyle(28, 'bold'),
    color: COLORS.text,
    letterSpacing: -0.5,
  },
  profileButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    overflow: 'hidden',
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },

  // Clean Search Styles
  searchContainer: {
    marginTop: SPACING.sm,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.backgroundCard,
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    ...SHADOWS.small,
  },
  searchIcon: {
    fontSize: 16,
    color: COLORS.textLight,
    marginRight: SPACING.md,
  },
  searchInput: {
    flex: 1,
    ...createTextStyle(16, 'regular'),
    color: COLORS.text,
    paddingVertical: 0,
  },
  searchPlaceholder: {
    flex: 1,
    ...createTextStyle(16, 'regular'),
    color: COLORS.textSecondary,
    paddingVertical: 0,
  },

  // Search Overlay Styles
  searchOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: COLORS.background,
    zIndex: 1000,
  },
  searchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
    paddingBottom: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.backgroundCard,
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    marginRight: SPACING.lg,
  },
  searchInputIcon: {
    fontSize: 16,
    color: COLORS.textSecondary,
    marginRight: SPACING.md,
  },
  searchInputField: {
    flex: 1,
    ...createTextStyle(16, 'regular'),
    color: COLORS.text,
    paddingVertical: 0,
  },
  searchCancelButton: {
    paddingVertical: SPACING.sm,
  },
  searchCancelText: {
    ...createTextStyle(16, 'medium'),
    color: COLORS.primary,
  },
  searchContent: {
    flex: 1,
  },
  searchLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: SPACING.xxl,
  },
  searchLoadingText: {
    ...createTextStyle(16, 'regular'),
    color: COLORS.textSecondary,
    marginTop: SPACING.md,
  },
  searchEmptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
    paddingTop: SPACING.xxl,
  },
  searchEmptyIcon: {
    fontSize: 48,
    marginBottom: SPACING.lg,
  },
  searchEmptyTitle: {
    ...createTextStyle(20, 'bold'),
    color: COLORS.text,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  searchEmptyText: {
    ...createTextStyle(16, 'regular'),
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  searchResultsList: {
    paddingHorizontal: SPACING.xl,
    paddingTop: SPACING.lg,
  },
  searchResultItem: {
    flexDirection: 'row',
    backgroundColor: COLORS.backgroundCard,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
    marginBottom: SPACING.md,
    ...SHADOWS.small,
  },
  searchResultImageContainer: {
    width: 60,
    height: 60,
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
    marginRight: SPACING.lg,
  },
  searchResultImage: {
    width: '100%',
    height: '100%',
  },
  searchResultImagePlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: COLORS.backgroundSecondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchResultImagePlaceholderText: {
    fontSize: 24,
  },
  searchResultContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  searchResultName: {
    ...createTextStyle(16, 'bold'),
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  searchResultLocation: {
    ...createTextStyle(14, 'regular'),
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
  },
  searchResultMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchResultCategory: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
    marginRight: SPACING.sm,
  },
  searchResultCategoryText: {
    ...createTextStyle(12, 'bold'),
    color: COLORS.white,
  },
  searchResultDifficulty: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    backgroundColor: COLORS.backgroundSecondary,
    borderRadius: BORDER_RADIUS.sm,
  },
  searchResultDifficultyText: {
    ...createTextStyle(12, 'medium'),
    color: COLORS.textSecondary,
  },

  // Content Styles
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingBottom: NAVIGATION.TAB_BAR_PADDING, // Add padding to avoid tab bar overlap
  },

  // Categories Section
  categoriesSection: {
    paddingHorizontal: SPACING.xl,
    paddingTop: SPACING.lg,
    paddingBottom: SPACING.xl,
  },
  categoriesGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  categoryItem: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: SPACING.xs,
  },
  categoryIconContainer: {
    width: 56,
    height: 56,
    borderRadius: BORDER_RADIUS.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  categoryIcon: {
    fontSize: 24,
  },
  categoryLabel: {
    ...createTextStyle(12, 'medium'),
    color: COLORS.text,
    textAlign: 'center',
  },



  // Section Styles
  section: {
    paddingHorizontal: SPACING.xl,
    marginBottom: Platform.OS === 'android' ? SPACING.xxl : SPACING.xl, // Extra spacing on Android to avoid visual overlap
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    ...createTextStyle(20, 'bold'),
    color: COLORS.text,
  },
  viewAllText: {
    ...createTextStyle(14, 'medium'),
    color: COLORS.primary,
  },

  // Featured Cards Styles
  featuredList: {
    paddingHorizontal: SPACING.xl, // Add padding on both sides
    paddingRight: SPACING.xl, // Ensure right padding for last card
    paddingBottom: Platform.OS === 'android' ? SPACING.lg : 0, // Extra bottom padding on Android
  },
  featuredCard: {
    width: Platform.OS === 'android' ? Math.min(width * 0.68, 270) : Math.min(width * 0.7, 280), // Slightly smaller on Android for better fit
    marginRight: SPACING.lg,
    backgroundColor: COLORS.backgroundCard,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
    ...SHADOWS.medium,
    minHeight: 240, // Ensure consistent card height
  },
  featuredImage: {
    width: '100%',
    height: 140,
    backgroundColor: COLORS.backgroundSecondary,
  },
  featuredContent: {
    padding: SPACING.lg,
    flex: 1, // Allow content to expand
    justifyContent: 'space-between', // Distribute content evenly
  },
  featuredHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.sm,
    minHeight: 40, // Ensure consistent header height
  },
  featuredTitle: {
    ...createTextStyle(16, 'bold'),
    color: COLORS.text,
    flex: 1,
    marginRight: SPACING.sm,
    lineHeight: 20, // Better line spacing
    numberOfLines: 2, // Allow 2 lines for longer names
  },
  ratingContainer: {
    backgroundColor: COLORS.backgroundSecondary,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
  },
  ratingText: {
    ...createTextStyle(12, 'medium'),
    color: COLORS.text,
  },
  featuredLocation: {
    ...createTextStyle(13, 'regular'),
    color: COLORS.textSecondary,
    marginBottom: SPACING.md,
    lineHeight: 18, // Better line spacing
    flexWrap: 'wrap', // Allow text wrapping
  },
  featuredFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap', // Allow wrapping if needed
    gap: SPACING.xs, // Add gap between elements
  },
  featuredDifficulty: {
    ...createTextStyle(12, 'medium'),
    color: COLORS.success,
    backgroundColor: COLORS.success + '15',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
    textAlign: 'center',
    minWidth: 60, // Ensure consistent width
  },
  featuredDuration: {
    ...createTextStyle(12, 'regular'),
    color: COLORS.textSecondary,
    textAlign: 'right',
    flex: 1, // Allow to take remaining space
  },

  // Quick Actions Section
  quickActionsSection: {
    paddingHorizontal: SPACING.xl,
    marginBottom: SPACING.xl,
    marginTop: Platform.OS === 'android' ? SPACING.lg : 0, // Extra top margin on Android for better separation
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionCard: {
    width: (width - SPACING.xl * 2 - SPACING.md) / 2,
    backgroundColor: COLORS.backgroundCard,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
    alignItems: 'center',
    marginBottom: SPACING.md,
    ...SHADOWS.small,
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: COLORS.primary + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  quickActionEmoji: {
    fontSize: 24,
  },
  quickActionTitle: {
    ...createTextStyle(14, 'medium'),
    color: COLORS.text,
    textAlign: 'center',
  },



  // Bottom Spacing
  bottomSpacing: {
    height: SPACING.xl,
  },

  // Loading States
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
  },
  loadingText: {
    ...createTextStyle(18, 'medium'),
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  loadingSubtext: {
    ...createTextStyle(14, 'regular'),
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  errorText: {
    ...createTextStyle(12, 'regular'),
    color: COLORS.warning,
    textAlign: 'center',
    marginTop: SPACING.sm,
  },

  // Lazy loading placeholder styles
  imagePlaceholder: {
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  placeholderContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholderIcon: {
    fontSize: 24,
    marginBottom: 4,
    opacity: 0.6,
  },
  placeholderText: {
    ...createTextStyle(12, 'medium'),
    color: COLORS.textSecondary,
    opacity: 0.7,
  },

});

export default HomeScreen;
