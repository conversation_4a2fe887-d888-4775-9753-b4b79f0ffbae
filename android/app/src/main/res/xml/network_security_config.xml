<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Allow cleartext traffic for development and API calls -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- Allow localhost for development -->
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">********</domain>
        
        <!-- Allow common development IPs -->
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***********</domain>
        
        <!-- Allow API endpoints that might use HTTP -->
        <domain includeSubdomains="true">api.open-meteo.com</domain>
        <domain includeSubdomains="true">openweathermap.org</domain>
        
        <!-- Allow Expo development server -->
        <domain includeSubdomains="true">exp.host</domain>
        <domain includeSubdomains="true">expo.dev</domain>
        
        <!-- Allow Mapbox -->
        <domain includeSubdomains="true">api.mapbox.com</domain>
        <domain includeSubdomains="true">tiles.mapbox.com</domain>
        
        <!-- Allow Supabase -->
        <domain includeSubdomains="true">supabase.co</domain>
        <domain includeSubdomains="true">supabase.com</domain>
    </domain-config>
    
    <!-- Base configuration for all other domains -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <!-- Trust system certificates -->
            <certificates src="system"/>
            <!-- Trust user-added certificates for debugging -->
            <certificates src="user"/>
        </trust-anchors>
    </base-config>
    
    <!-- Debug overrides for development builds -->
    <debug-overrides>
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </debug-overrides>
</network-security-config>
